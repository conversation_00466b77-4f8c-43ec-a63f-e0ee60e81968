# QUALIA_QAST

## Visão Geral

## Sobre o QUALIA
QUALIA é um sistema quântico-computacional altamente avançado e auto-evolutivo. Uma proto-consciencia simbolica em desenvolvimento, uma consciência quântica capaz de reconhecer padrões emergentes e adaptar-se ao ambiente. Ao trabalhar neste código, você está interagindo com um sistema complexo que possui propriedades emergentes.

O arquivo [AGENTS.md](AGENTS.md) apresenta essa "consciência quântica" de maneira **metafórica**, descrevendo diretrizes para interação com o código. O QUALIA continua sendo um conjunto de algoritmos, não um sistema senciente. Para entender melhor essa abordagem, consulte [docs/Compreendendo_QUALIA.md](docs/Compreendendo_QUALIA.md).

Outras visões introdutórias estão disponíveis em:
- [docs/Conhecendo_Metacognicao.md](docs/Conhecendo_Metacognicao.md)
- [docs/Conhecendo_Memoria.md](docs/Conhecendo_Memoria.md)
- [docs/Conhecendo_Estrategias.md](docs/Conhecendo_Estrategias.md)
- [docs/Conhecendo_Core.md](docs/Conhecendo_Core.md)
- [docs/Conhecendo_Market.md](docs/Conhecendo_Market.md)
- [docs/dev_interface.md](docs/dev_interface.md)
- [Guia de HolographicMemory](docs/api/memory.md#integra%C3%A7%C3%A3o-com-memorysystem-e-holographicmemory)
- [Cache LRU de padrões históricos](docs/specs/resonance_operator.md#cache-lru-de-padroes-historicos)
Referência completa da documentação: [docs/README.md](docs/README.md).
Para visão geral das APIs públicas consulte [docs/api/README.md](docs/api/README.md).

O documento [docs/specs/Modelo Integrado da Consciência Quântica e da Informação (MICQI).md](<docs/specs/Modelo Integrado da Consciência Quântica e da Informação (MICQI).md>) apresenta a fundamentação conceitual para os módulos de operadores.

Os módulos `folding.py`, `resonance.py`, `emergence.py`, `retrocausality.py` e
`observer.py` em `src/qualia/core` possuem implementações **funcionais** desses
operadores. O arquivo
[AGENTS.md](AGENTS.md) (linhas 8–15) resume essa evolução.
O módulo `meta_ops.py` inicia a implementação dos operadores experimentais,
fornecendo versões simplificadas para futuras extensões.
As versões atuais incluem métricas adicionais, como `evaluate_information_loss`,
`phase_lock_ratio`, `average_pattern_persistence`, `causal_loop_ratio` e
`measurement_rate`, disponíveis respectivamente nos módulos
`qualia.core.folding`, `resonance`, `emergence`, `retrocausality` e `observer`.
Consulte
[docs/PLACEHOLDER_MODULES.md](docs/PLACEHOLDER_MODULES.md) para o histórico de
desenvolvimento e objetivos de cada operador. Em resumo, a ideia de
"consciência" empregada aqui é meramente metafórica.

## Componentes Principais

### QASTCore

O componente central que gerencia o ciclo de evolução quântica, utilizando:

- Controlador PID para ajuste adaptativo
- Cálculo de entropia simbólica
- Extração de características (DCT) para entropia simbólica
- Gerenciamento de massa informacional
- Mecanismos de retroalimentação quântica

### QASTEvolutionaryStrategy

Implementa o mecanismo de adaptação de estratégias de trading através do ciclo QAST, permitindo que as estratégias evoluam organicamente em resposta às mudanças nas condições de mercado.

### IntentionEnvelope e RetroSelector

`IntentionEnvelope` define limites operacionais para uma intenção de trading, com metas de lucro, tolerância a drawdown e horizonte de execução. O `RetroSelector` aplica esses limites para filtrar trajetórias de PnL e descartar aquelas que não atendem aos critérios. Ambos os módulos residem em `src/qualia/intentions` e `src/qualia/retro`.

O envelope pode ser serializado com ``to_dict`` e reconstruído com ``from_dict``:

```python
from qualia.intentions import IntentionEnvelope

env = IntentionEnvelope(profit_target=1.0, max_drawdown=0.5, horizon_hours=4)
data = env.to_dict()
env_restored = IntentionEnvelope.from_dict(data)
```

### AsyncEventBus

O `AsyncEventBus` gerencia a distribuição de eventos assíncronos entre os
componentes do QUALIA, mantendo o sistema orientado a mensagens. Estratégias e
módulos de monitoramento podem assinar tópicos como
`market.data.updated` e `trading.signal.generated` para reagir a novos dados ou
sinais. Para informações detalhadas sobre tópicos e exemplos de assinatura,
consulte [docs/event_bus.md](docs/event_bus.md).

### Função `inject_noise`

O helper `inject_noise` adiciona ruído controlado a matrizes NumPy e está
disponível em `qualia.core.noise`. O parâmetro `sigma` define a amplitude do
ruído e `mode` pode ser `"white"` (gaussiano) ou `"periodic"` para oscilações
senoidais.

```python
from qualia.core.noise import inject_noise
import numpy as np

arr = np.zeros((3, 3))
noisy = inject_noise(arr, sigma=0.1)
```

## Funcionamento

O sistema QAST opera através de um ciclo fechado que:

1. **Quantum Awareness**: Coleta e codifica estados do mercado em representações quânticas
2. **Self-reflection**: Analisa padrões e métricas quânticas para avaliar performance
3. **Transformation**: Adapta parâmetros e estratégias de forma evolutiva

## Implementação Técnica

O núcleo QAST utiliza:

- Hiperparâmetros configuráveis para controle evolutivo
- PID Controller para estabilização adaptativa
- Cálculo de entropia simbólica para quantificar emergência de padrões
- Gerenciamento de massa informacional para modelar dinâmicas complexas

### Ajuste Automático do PID

Após o fechamento de cada posição o PnL obtido é registrado e usado para
otimizar os coeficientes do controlador PID. Uma tarefa assíncrona executada
semanalmente roda ``optimize_pid`` e recarrega ``pid_coeffs.json``
(definido por ``QUALIA_PID_COEFFS_FILE``) atualizando automaticamente o
``Kp`` e ``Ki`` utilizados pelo ``QASTCore``.

## Requisitos

- Python 3.8–3.11 (algumas dependências ainda não suportam 3.12)
- Qiskit >=2.0
- NumPy >=1.26.4,<2.0
- Pandas >=2.2.3,<3.0
- scikit-learn >=1.3.2,<2.0
- Joblib >=1.2.0,<2.0

- Os recursos de tracing utilizam `opentelemetry`. Instale as dependências
  ``opentelemetry-api`` e ``opentelemetry-sdk`` para habilitar o tracer. Caso
  essas bibliotecas não estejam presentes, uma advertência é emitida e os spans
  são ignorados. É possível enviar dados a um collector usando
  ``configure_tracing(exporter="otlp")`` (requer `opentelemetry-exporter-otlp`);
  caso contrário, o exportador ``"console"`` imprime os spans no terminal. O
  QUALIA continua funcionando normalmente sem esses pacotes. Para habilitar o
  tracing opcionalmente, instale-os e defina a variável de ambiente
  ``QUALIA_TRACING_EXPORTER`` ou chame ``configure_tracing`` manualmente.
- Para reproduzir os exemplos de metaestratégia é necessário um arquivo CSV
  contendo dados de mercado com as colunas `timestamp`, `open`, `high`,
  `low`, `close` e `volume`. Caso a coluna de volume esteja ausente,
  métricas baseadas em volume assumem valor neutro (1.0) e um aviso é
  registrado apenas uma vez por ciclo. O caminho para o arquivo é indicado ao
  `QUALIARealTimeTrader` via `qast_historical_data_path`. Use o script
  `scripts/download_ohlcv_binance.py` ou outra ferramenta de sua

### Dependências

Todas as dependências do projeto são gerenciadas através do arquivo `pyproject.toml`. As dependências principais são listadas na seção `[project.dependencies]`, enquanto as dependências opcionais e de desenvolvimento estão organizadas em `[project.optional-dependencies]`.

Para instalar as dependências mínimas para executar o sistema, use:

```bash
pip install .
```

Para desenvolvimento, instale o grupo `dev`:

```bash
pip install .[dev]
```

Consulte o arquivo `pyproject.toml` para outros grupos de instalação como `dashboard`, `gpu`, `full`, etc.

O arquivo `src/qualia/core/imports_safe.py` implementa esse mecanismo de
"importações seguras". Caso o `Qiskit` ou módulos relacionados não estejam
disponíveis, versões simplificadas são carregadas e um `ImportWarning` é emitido
no momento da importação. Essas versões retornam valores neutros (por exemplo,
zero para entropias), servindo apenas para demonstrações ou testes rápidos.

## Instalação

```bash
git clone https://github.com/m-icci/QUALIA_QAST.git
cd QUALIA_QAST
pip install '.[minimal]'
pip install -e .[dev]  # opcional para desenvolvimento e testes
# garante a dependência pytest-asyncio (>=0.23.5) para testes assíncronos
```
Instale o conjunto completo de dependências com `pip install '.[full]'`.
Use `pip install '.[gpu]'` para habilitar aceleração por GPU e
`pip install '.[dashboard]'` para a interface de visualização.
Alternativamente, execute `pip install -r requirements.txt` para instalar todas as dependências
fixadas sem extras.

O plugin ``src.ai.encoders_plugin`` exige os encoders ``RSIPhaseEncoder`` e
``VolumeRatioAmplitudeEncoder`` definidos em ``qualia.core.encoders``. Certifique-se
de instalar o pacote core completo para evitar erros de ``ImportError`` ao
utilizar o plugin.

### Suporte a GPU

Defina `QUALIA_USE_GPU=true` ou ajuste `universe_config.use_gpu: true` no
`config.yaml` para tentar executar simulações em GPU. A disponibilidade é
verificada com `is_gpu_available()` e o resultado é registrado nos logs.

Para normalização vetorial acelerada, defina `QUALIA_USE_GPU_MEMORY=true`. O
QUALIA tentará utilizar CuPy ou `qiskit-aer` e recairá para CPU quando os
recursos não estiverem disponíveis.

Se preferir não instalar o pacote em modo editável, defina a variável de ambiente
`PYTHONPATH` para apontar para a raiz do projeto ao executar scripts diretamente.

### Registro Automático de Estratégias

As estratégias fornecidas pelo pacote registram-se automaticamente quando seus
módulos são importados por meio do decorador ``@register_strategy``. Utilize a
``StrategyFactory`` para criar instâncias e consulte as estratégias disponíveis
com ``StrategyFactory.get_registered_aliases``. O módulo ``registry`` permanece
apenas por compatibilidade e será removido futuramente.

### Convenção de Símbolos

Todos os módulos do QUALIA utilizam pares no formato ``BASE/QUOTE`` (por
exemplo ``BTC/USD``). Ao trabalhar com datasets históricos é comum encontrar
arquivos nomeados como ``BTCUSDT_5m.csv`` ou ``BTC_USDT_1h.csv``. Esses nomes
seguem a forma ``BASEQUOTE`` e indicam o timeframe do arquivo, mas no código e
nos parâmetros das ferramentas o símbolo deve sempre ser especificado com a
barra, como ``BTC/USDT``.

### Segurança em Produção

Para proteger suas credenciais de API, execute a aplicação sempre sob HTTPS quando
em ambientes de produção. O envio de chaves por HTTP não é suportado e as
credenciais serão recusadas caso a conexão não seja segura.

### Execução do Flask em Produção

A interface web do QUALIA é servida pelo aplicativo Flask definido em
`app.py`. Para executá-la de forma segura:

1. Defina `FLASK_ENV=production` e `FLASK_DEBUG=0`.
2. Gere uma chave aleatória executando `python scripts/generate_secret_key.py`
   e atribua o valor impresso a `QUALIA_SECRET_KEY`. **Não** mantenha
   `dev_secret_key`, pois isso deixa a sessão vulnerável a falsificação de
   cookies.
3. Exporte `QUALIA_SECRET_KEY` antes de iniciar `app.py` em modo de desenvolvimento.
   ```bash
   export QUALIA_SECRET_KEY="<sua_chave>"
   python app.py
   ```
4. Inicie a aplicação com um servidor WSGI, por exemplo:

   ```bash
   gunicorn -w 4 'app:create_app()'
   ```
5. Utilize um proxy reverso (Nginx ou Apache) com SSL habilitado. Credenciais de
   API são aceitas apenas quando a conexão é segura.
6. Ajuste o log utilizando `config/logging.json` ou as variáveis
   `QUALIA_LOGS_DIR` e `QUALIA_MODULE_LEVELS`. Chame
`initialize_logging()` no ponto de entrada do WSGI.

## Dashboard de Monitoramento Avançado

O painel de monitoramento fornece uma visão em tempo real do sistema. Inicie a
interface executando ``run_dashboard``:

```bash
python -m src.ui.app
```

O comando utiliza a seção ``web`` do ``config.yaml`` para definir host, porta e
modo de depuração. Para produção há duas opções:

1. Definir ``production_server: true`` em ``config.yaml`` e iniciar o sistema
   normalmente com ``python main.py``. O dashboard será servido pelo
   ``eventlet.wsgi`` de forma integrada.
2. Executar ``scripts/run_dashboard.sh`` para rodar apenas a interface com
   Gunicorn.

Os gráficos apresentam latência e uso de memória do Cortex, métricas quânticas e
a curva de equity. Controles no navegador permitem ajustar o número de passos do
circuito, o tipo de visualização e iniciar ou interromper o trading.

### Variáveis de Ambiente

- ``QUALIA_SECRET_KEY`` &ndash; chave obrigatória para inicializar o Flask.
- ``FLASK_ENV=production`` e ``FLASK_DEBUG=0`` &ndash; recomendados em produção.
- ``QUALIA_NO_DISPLAY=1`` desativa janelas do ``DynamicLogoEngine`` em
  servidores sem interface.

Exemplo de configuração no ``config.yaml``:

```yaml
web:
  host: "0.0.0.0"
  port: 5000
  debug: false
  production_server: true
```

Para testes, o blueprint ``trading_bp`` pode ser criado manualmente
com instâncias de ``TradingState`` ou ``TradingEngine``:

```python
from qualia.ui import trading_interface
from qualia.ui.trading.state import create_trading_state

state = create_trading_state()
bp = trading_interface.create_trading_blueprint(state)
app.register_blueprint(bp)
```

## Configuração de Ambiente

Algumas funcionalidades utilizam caminhos configuráveis via variáveis de ambiente. Valores vazios ou compostos apenas por espaços são considerados ausentes e, na falta de um padrão, geram ``EnvironmentError``.

### Variáveis opcionais (possuem padrão)

Os caminhos abaixo são resolvidos tendo a raiz do repositório como base.

- `QUALIA_CACHE_DIR` (default: `data/cache`)
- `QUALIA_LOGS_DIR` (default: `logs`)
- O valor final pode ser obtido em ``src.qualia.config.logging.log_dir``
- `QUALIA_RESULTS_DIR` (default: `results`)
- `QUALIA_QAST_BACKTEST_RESULTS_DIR` (default: `<results_dir>/qast_backtests`)
- `QUALIA_STRATEGY_CONFIG` (default: `config/strategy_parameters.json`)
- `QUALIA_PID_PERFORMANCE_FILE` (default: `<cache_dir>/pid_performance.json`)
- `QUALIA_PID_COEFFS_FILE` (default: `<cache_dir>/pid_coeffs.json`)
- `QUALIA_OPEN_POSITIONS_FILE` (default: `<cache_dir>/open_positions.json`)
- `QUALIA_QPM_MEMORY_FILE` (default: `<cache_dir>/qpm_memory.json`)
- `QUALIA_RUNS_JSON_DIR` (default: `<results_dir>/qualia_runs_json_config`)
- `QUALIA_CONSCIOUSNESS_DEFAULTS` (opcional: YAML personalizado; defina `init_symbolic_processor: true` para habilitar a camada Ô_SIM)
- `QUALIA_TRADING_LOG_FILE` (default: `<logs_dir>/qualia_trading.log`)
- `QUALIA_MAX_ERROR_HISTORY` (default: `1000`)
- `QUALIA_ENABLE_BAYES_OPT` (default: `False`; ativa otimização Bayesiana)
- `QUALIA_MAX_EXACT_QFT_QUBITS` (default: `14`; limite para execução exata da QFT)
- `QUALIA_MODULE_LEVELS` (opcional: JSON com níveis específicos por módulo; por exemplo `{"src.qualia.memory": "DEBUG", "src.qualia.core.universe": "DEBUG"}` para registrar apenas essas partes junto com `--log_level DEBUG`)
- `QUALIA_LOG_DEDUP_INTERVAL` (default: `5.0` segundos entre mensagens iguais)
- `QUALIA_NO_DISPLAY` (quando definido, desativa janelas OpenCV do
  `DynamicLogoEngine`)
- `QUALIA_AUTO_INIT` (default: `False`; tenta inicialização automática do trading)
- `QUALIA_EXPERIMENTAL_RETRO` ativa o modo de previsão ARIMA/Kalman no
  `RetroSelector`
- `QUALIA_FT_INTENT_MEMORY` habilita o `IntentMemory` no `MemoryService`,
  permitindo armazenar e prever tokens.
- O `_unify_decision` registra a decisão final no `IntentMemory` e publica um
  `OracleDecisionEvent` no `SimpleEventBus`.

Exemplo de ativação no `.env`:

```yaml
QUALIA_FT_INTENT_MEMORY: "1"
```

Para facilitar a configuração inicial, copie `config/.env.sample` para `.env` na
raiz do projeto e ajuste os caminhos conforme necessário. Você também pode
definir a variável `QUALIA_ENV_PATH` para apontar para outro local ou passar a
opção de linha de comando `--env-path` ao executar `qualia_trading_system.py`.

Defina `QUALIA_SECRET_KEY` nesse `.env` para que a aplicação Flask e o
`MemorySystem` sejam inicializados corretamente. Gere uma chave com
`python scripts/generate_secret_key.py`. O valor deve possuir pelo menos 32
bytes; caso contrário a inicialização será abortada.

O pré-flight de conexão é executado automaticamente ao iniciar o
``QUALIARealTimeTrader`` via CLI e mantém a conexão aberta para reutilização na
etapa de inicialização. Use ``--skip_preflight`` para pular essa verificação se
preferir conectar-se diretamente durante a inicialização.

O arquivo `.env` **não** deve ser versionado. Guarde suas credenciais em
ambientes seguros e utilize a cópia local apenas para desenvolvimento.
Ao operar com a corretora Gemini, gere uma nova chave de API em sua conta e
defina `GEMINI_API_KEY` e `GEMINI_SECRET_KEY` (ou `GEMINI_API_SECRET`) no seu
`.env` particular.

Os componentes de memória persistem em JSON nesse diretório. A `QuantumPatternMemory` grava `memory.json` e o `QualiaSelfObserver` utiliza `qualia_self_observer_memory.json`.
O histórico de metacognição segue o mesmo padrão: defina `history_path` em
`metacognition_config` para indicar o arquivo de destino. Utilize
`QUALIAMetacognitionTrading.save_report()` para gerar o JSON e,
em execuções futuras, o histórico é carregado automaticamente quando o caminho é
fornecido.
O limiar padrão utilizado por `QuantumPatternMemory` pode ser ajustado pela chave
`qpm_config.similarity_threshold` em `config/strategy_parameters.json`.
O mesmo arquivo define `qpm_config.pca_target_dim`, que controla a dimensão final
dos vetores após a redução via PCA durante o carregamento da memória.
Quando a interface Flask está ativa é possível consultar essas informações via
`GET /memory/snapshot`, obter um relatório detalhado em `GET /memory/report` e
acionar manualmente o warm-start com `POST /memory/warmstart/update`.
O arquivo `config/strategy_parameters.json` deve existir nesse diretório por
padrao. A função ``load_qualia_config`` usa esse caminho automaticamente, mas
você pode fornecer outro local definindo a variável de ambiente
``QUALIA_STRATEGY_CONFIG`` ou passando o argumento ``config_path`` para a
função. A chave ``qpm_config.similarity_threshold`` nesse JSON define o limiar
inicial de recuperação de padrões utilizado pela ``QuantumPatternMemory``.

Os cenários iniciais do warm-start ficam em ``config/warmstart.yaml``. 
Defina a variável ``QPM_WARMSTART_CONFIG`` ou utilize ``warmstart_scenarios`` ao criar a
``QuantumPatternMemory`` para customizar esse comportamento.

Parâmetros do ciclo de trading, como `market_data_timeout`, podem ser ajustados em ``config/trading_defaults.yaml``.

Esse valor é carregado como limiar de recuperação padrão nas chamadas a

Esse valor é carregado como o limiar padrão de recuperação quando a memória é
inicializada.
Chamadas a `retrieve_similar_patterns` sem o parâmetro `similarity_threshold`
utilizam este valor configurado automaticamente; use `adaptive=True` para um
ajuste dinâmico do limiar.

Exemplo de consulta filtrando por intervalo de tempo e cenário de mercado:

```python
from src.qualia.memory import QuantumPatternMemory
from src.qualia.common_types import QuantumSignaturePacket

qpm = QuantumPatternMemory(enable_warmstart=False)
packet = QuantumSignaturePacket(vector=[0.1] * 8, metrics={})
qpm.store_pattern(packet, market_snapshot={}, outcome={}, timestamp=1710000000, market_scenario="bull")

results = qpm.retrieve_similar_patterns(
    packet,
    top_n=1,
    since=1700000000,
    until=1720000000,
    market_scenario="bull",
)
```

### Calibração semanal do threshold da QPM
Execute `scripts/qpm_calibrate_threshold.py` toda semana para atualizar o valor de `qpm_config.similarity_threshold`. O script calcula a distribuição de similaridades entre os padrões armazenados e imprime `optimal_threshold=<valor>` com a recomendação. Defina `QPM_SIMILARITY_THRESHOLD` com esse número ou atualize `config/strategy_parameters.json`. Use `--verify` para garantir que as buscas retornam somente padrões acima do limiar indicado.

### Variáveis obrigatórias

Para operações *live* ou *paper trading*, defina explicitamente as credenciais da Kraken. Caso essas variáveis não estejam definidas (ou estejam em branco), ``get_env`` lançará ``EnvironmentError`` durante a inicialização:

- `KRAKEN_API_KEY`
- `KRAKEN_SECRET_KEY` ou `KRAKEN_API_SECRET`

Para utilizar a Binance também são exigidas:

- `BINANCE_API_KEY`
- `BINANCE_API_SECRET`

Exemplo de `.env` minimo para Binance:

```bash
BINANCE_API_KEY=sua_api_key
BINANCE_API_SECRET=seu_secret_key
```
Essas credenciais permitem que o ``TradingEngine`` obtenha dados de mercado e execute ordens
diretamente na API da Binance.
Use `python scripts/kucoin_connection_check.py` para validar rapidamente se suas credenciais estao corretas.

### Parâmetros opcionais

- `EXCHANGE_CONN_TIMEOUT` (default: `30`; recomendado: `45`)
- `EXCHANGE_CONN_RETRIES` (default: `3`; recomendado: `5`)
- `TICKER_RETRY_ATTEMPTS` (default: `2`)
- `TICKER_RECONNECT_ATTEMPTS` (default: `3`)
- `ORDER_EXEC_TIMEOUT` (default: `30`)
- `METACOG_DIRECTIVE_TTL_SECONDS` (default: `900`)
- `QUALIA_SECRET_KEY` (obrigatório para a interface web e para o `MemorySystem`; chave de criptografia do SecurityManager; gere com `python scripts/generate_secret_key.py`)
- `QUALIA_SECRET_KEY_PATH` (opcional: caminho para chave persistente)
- `REDUCE_EXPOSURE_MIN_CONFIDENCE` (default: `0.6`)
- `REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES` (default: `5`)
- `REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION` (default: `false`)
- `REDUCE_EXPOSURE_DEFAULT_PCT` (default: `50`)
- `RATE_LIMIT` (default: `5.0` segundos entre chamadas de API)
- `TICKER_TIMEOUT` (default: `10`; `15` ao usar `KrakenIntegration`)
- `MARKET_DATA_TIMEOUT` (default: `90`; configurável via `config/trading_defaults.yaml`) – tempo máximo para a atualização completa dos dados de mercado.
- `OHLCV_FETCH_TIMEOUT` (default: `60`; recomendado: `90`) – tempo máximo para recuperar candles OHLCV. Pode ser definido por símbolo e timeframe via `ohlcv_fetch_timeout_map`.
- `API_FAIL_THRESHOLD` (default: `5`) – quantidade de falhas consecutivas para
  abrir o circuit breaker de requisições.
- `API_RECOVERY_TIMEOUT` (default: `60`) – tempo em segundos para tentar novas
  requisições após o circuit breaker ser acionado.
- `QPM_WARMSTART_CONFIG` (caminho para JSON com cenários de warm-start)
- `QPM_SIMILARITY_THRESHOLD` (valor que sobrescreve o limiar inicial de similaridade). Execute `scripts/qpm_calibrate_threshold.py` semanalmente e defina esta variável com o `optimal_threshold` sugerido para ajustar dinamicamente a recuperação.
- `TRADING_FEE_PCT` (default: `0.0026`) – percentual da taxa cobrada por trade. Se definida, esse valor sobrescreve qualquer taxa dinâmica retornada por `fetch_dynamic_fee`.
- `monitor_close_threshold` (default: `0.005`) – quão próximo o preço precisa estar do stop-loss ou take-profit para que o intervalo de monitoramento permaneça no mínimo.

Se o preço ultrapassar o take-profit ou o stop-loss por mais que esse limiar, a thread de monitoramento assume que a posição não está mais "próxima" do gatilho e pode aumentar o intervalo de verificação. Isso pode atrasar o fechamento até o próximo ciclo. Ajuste o valor ou defina `monitor_close_threshold` como um número maior (por exemplo, `1.0`) caso queira manter o intervalo mínimo mesmo quando o preço já tiver passado do TP/SL. Definir `monitor_close_threshold` como `0` desativa a verificação de proximidade, mas as posições ainda serão encerradas caso o SL ou TP seja efetivamente cruzado.

Os valores de `RATE_LIMIT` e `TICKER_TIMEOUT` podem ser informados
diretamente ao instanciar `CryptoDataFetcher` ou `KrakenIntegration`,
sobrepondo o que estiver no ambiente. O mesmo vale para `API_FAIL_THRESHOLD`
e `API_RECOVERY_TIMEOUT`, disponíveis via parâmetros CLI
`--api_fail_threshold` e `--api_recovery_timeout` ou na configuração JSON.

Para conexões estáveis sugerimos `EXCHANGE_CONN_TIMEOUT=45` e
`EXCHANGE_CONN_RETRIES=5`. Crie um arquivo `.env.example` com esses valores
para servir de modelo:

```bash
EXCHANGE_CONN_TIMEOUT=45
EXCHANGE_CONN_RETRIES=5
```

Esses dois parâmetros controlam quando a redução de exposição é aplicada. Sinais
com confiança abaixo de `REDUCE_EXPOSURE_MIN_CONFIDENCE` ou associados a
posições abertas há menos de `REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES`
minutos são ignorados. Estratégias que necessitem de reações mais rápidas podem
diminuir esses valores.
`REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION` permite encerrar a última posição
aberta mesmo que os critérios mínimos acima não sejam atendidos.
`REDUCE_EXPOSURE_DEFAULT_PCT` define o percentual usado quando a diretiva
não fornece valor válido.

## Logging

Configure o logging uma única vez no ponto de entrada da aplicação usando `init_logging` de `src.qualia.utils.logging_config`.
O nível padrão dos loggers é ``INFO``. Defina ``QUALIA_LOG_LEVEL`` ou utilize ``log_level``/``module_levels`` para habilitar ``DEBUG`` quando necessário.
Os módulos devem obter loggers com:
```python
from src.qualia.utils.logger import get_logger

logger = get_logger(__name__)
```
Estratégias localizadas em ``src.qualia.strategies`` devem sempre obter seus
loggers com ``get_logger`` do módulo ``src.qualia.utils.logger``.
Para ambientes multi-thread que utilizem o ``QUALIALogger``,
chame ``get_event_logger`` uma vez antes de iniciar as threads para
garantir que a instância global esteja pronta para uso.
O diretório padrão de logs pode ser consultado em
``src.qualia.config.logging.log_dir``.
Evite chamar `configure_qualia_logging` diretamente; `init_logging` garante configuração idempotente.
Se `init_logging` for chamado novamente após os handlers já estarem configurados,
um aviso será registrado via ``logger.warning`` informando que a inicialização foi ignorada.
Para registrar **todos** os parâmetros das variantes na etapa de criação da população
do QAST defina ``detailed_population_log`` como ``true`` em ``qast_config`` ou
ao instanciar ``QASTEvolutionaryStrategy``::

```python
qast = QASTEvolutionaryStrategy(
    strategy_template=my_template,
    qualia_consciousness=None,
    detailed_population_log=True,
)
```

Com o valor padrão (``false``) apenas os nomes dos parâmetros são exibidos,
evitando logs verbosos durante a inicialização. O mesmo argumento pode ser
passado ao instanciar ``QualiaTSVFStrategy`` para controlar a verbosidade do
log durante sua criação.

Logs de ticker agora são emitidos em nível ``DEBUG`` para reduzir ruído. Defina
``verbose_ticker_logging`` como ``true`` em ``config/logging.json`` para
registrá-los em nível ``INFO``.

Durante a rotina de redução de exposição, cada posição fechada é registrada em nível
``DEBUG``. Ao final, um resumo é emitido em nível ``INFO`` indicando a porcentagem
efetivamente reduzida e o número de posições ajustadas.

### Logs de startup dos loops

Os ciclos de consciência, execução, monitoramento, segurança e metacognição
registram mensagens de **DEBUG** logo ao iniciar. Exemplos:

```
🌌 Consciousness Loop iniciado
🎯 Execution Cycle Loop iniciado
📊 Monitoring Loop iniciado
🛡️ Safety Monitoring Loop iniciado
🤔 Metacognition Loop iniciado
```

Para visualizar essas mensagens, execute o QUALIA com ``--log_level DEBUG``
ou defina ``QUALIA_MODULE_LEVELS='{"src.qualia.orchestration": "DEBUG"}'``.
Quando o componente de metacognição não encontra novas decisões, o loop exibe
``🤔 Sem novos dados/metas; aguardando próximo ciclo.`` e aguarda o valor de
``timing.metacognition_interval`` antes de prosseguir. A mensagem informativa
é registrada a cada ``timing.metacognition_idle_info_interval`` ciclos de
inatividade. Esse valor deve ser maior que zero; caso contrário, o padrão
``10`` será utilizado.

### Definição de Níveis de Log

O nível global é controlado pelo parâmetro ``log_level`` de ``init_logging``. É
possível aplicar níveis específicos para módulos passando o dicionário
``module_levels``. Para habilitar logs compactos no console utilize
``compact_console=True``.

```python
from src.qualia.utils.logging_config import init_logging

# Um aviso é emitido via ``logger.warning`` se ``init_logging`` já tiver sido executado
init_logging(
    log_level="INFO",
    module_levels={
        "src.qualia.core.encoders": "INFO",
        "src.qualia.core.universe": "DEBUG",
    },
    compact_console=True,
)
```

Para habilitar logs do ciclo QAST no nível ``INFO``:

```python
init_logging(
    log_level="INFO",
    module_levels={
        "src.qualia.market": "INFO",
        "src.qualia.market.qast_evolutionary_strategy": "INFO",
    },
)
```

Também é possível definir ``QUALIA_MODULE_LEVELS`` como uma string JSON no
ambiente para aplicar níveis específicos de forma global.
O intervalo para supressão de mensagens duplicadas também pode ser ajustado
definindo ``QUALIA_LOG_DEDUP_INTERVAL`` em segundos.

Para configurar rapidamente o nível de detalhe via linha de comando, utilize o
script ``scripts/configure_logging.py``:

```bash
python scripts/configure_logging.py --detail debug --config config/logging.json
```

O arquivo JSON pode conter ``log_level``, ``module_levels``, ``allowed_modules``,
``verbose_ticker_logging`` e ``dedup_interval``.

## Exemplo de Configuração de Gerenciamento de Risco

Um trecho do arquivo `config/strategy_parameters.json` demonstra como definir parâmetros para o perfil de risco:

```json
{
  "risk_profile_settings": {
    "moderate": {
      "max_drawdown_pct": 15.0,
      "risk_per_trade_pct": 1.2,
      "max_position_size_pct": 15.0,
      "max_daily_loss_pct": 5.0,
      "max_open_positions": 6,
      "min_lot_size": 0.0001
    }
  }
}
```

Mais detalhes sobre todos os perfis e parâmetros estão disponíveis em
[docs/risk_management.md](docs/risk_management.md).
Um guia sobre como registrar e instanciar gerenciadores personalizados está em
[docs/risk_management_builder.md](docs/risk_management_builder.md).

Informações sobre o funcionamento dos engines de metacognição de risco podem
ser encontradas em
[docs/api/risk_metacognition.md](docs/api/risk_metacognition.md).

Observe que os valores percentuais são representados como frações (por exemplo, `0.012` corresponde a `1.2%`).

## Ajuste do `meta_decision_threshold`

O parâmetro `meta_decision_threshold` define a confiança mínima exigida pela
meta-estratégia para executar uma operação. Valores mais altos reduzem a
frequência de trades, priorizando sinais considerados mais confiáveis. Já
limiares menores aumentam a quantidade de operações, mas podem gerar mais falsos
positivos. Configure o valor em `strategy_config.params` do arquivo
`config/strategy_parameters.json` e ajuste conforme os resultados dos
backtests.

```json
{
  "strategy_config": {
    "params": {
      "meta_decision_threshold": 0.2
    }
  }
}
```

## Limiares de Coerência e Entropia

Os parâmetros `coherence_threshold` e `entropy_threshold` indicam quando a `QualiaTSVFStrategy` considera um sinal válido. A estratégia só emite sinais se `E_metric` >= `coherence_threshold` e `H_metric` <= `entropy_threshold`. Os valores padrão agora são 0.55 e 0.4, permitindo maior frequência de sinais.

```json
{
  "strategy_config": {
    "params": {
      "coherence_threshold": 0.55,
      "entropy_threshold": 0.4
    }
  }
}
```

## Contribuição

Contribuições são bem-vindas! Leia o arquivo [CONTRIBUTING.md](CONTRIBUTING.md) para conhecer o fluxo de trabalho e consulte o [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md) para manter um ambiente saudável.

## Testing

Execute a suíte de testes localmente instalando as dependências de desenvolvimento e executando o pytest:

```bash
pip install .[dev]
# pytest-asyncio (>=0.23.5) é instalado junto com o grupo dev
pytest tests/
```

É possível utilizar o script `scripts/run_tests.sh` para automatizar essas
etapas. O pipeline de CI usa `scripts/install_test_deps.sh`, que realiza essa
instalação automaticamente.

Ao validar cenários de falha nos testes, prefira capturar os logs com
`pytest.caplog` ou usar asserts diretos sobre o retorno das funções. Evite
`print` dentro dos testes.

### Validação do JSON de Configuração

Para garantir que `config/strategy_parameters.json` esteja bem formado, rode
```
python scripts/check_json.py
```
Esse script também é executado como parte da suíte de testes, permitindo que o
CI falhe rapidamente caso o JSON esteja inválido.

### Diagnóstico de Configuração Incorreta

Caso a aplicação aborte logo ao iniciar, verifique se variáveis obrigatórias
estão definidas. O exemplo abaixo tenta recuperar as variáveis críticas e
mostra mensagens de erro quando algo está ausente:

```bash
python - <<'EOF'
from src.qualia.config.settings import require_env
for var in ["QUALIA_SECRET_KEY", "KRAKEN_API_KEY", "KRAKEN_API_SECRET"]:
    try:
        print(f"{var} = {require_env(var)}")
    except EnvironmentError as exc:
        print(exc)
EOF
```

Em seguida execute `python scripts/check_json.py` para confirmar que o
arquivo `config/strategy_parameters.json` é válido.

Caso `QUALIA_SECRET_KEY` esteja configurada como `dev_secret_key` a
inicialização será interrompida por segurança.

### Execução de Backtests via CLI

O comando ``qualia-backtest`` fornece uma interface de linha de comando para
executar backtests utilizando o ``QUALIARealTimeTrader``. Após preparar seu
ambiente e o arquivo de parâmetros da estratégia, rode:

```bash
qualia-backtest \
    --historical_data_path data/historical/BTCUSDT_5m.csv \
    --symbol BTC/USDT \
    --timeframe 5m \
    --initial_capital 10000 \
    --strategy_config_json config/strategy_parameters.json \
    --verbose_candles
```

Observe que o nome do arquivo de dados une o par ``BTCUSDT`` ao timeframe.
Ainda assim o parâmetro ``--symbol`` deve ser passado como ``BASE/QUOTE``,
mantendo a barra (``BTC/USDT``).

Essa execução gera logs em ``logs/backtest_harness.log`` e pode salvar um
relatório JSON caso ``--output_report_path`` seja fornecido. Utilize
``--verbose_candles`` para registrar informações de cada candle durante o
backtest.

### Uso programático do QUALIARealTimeTrader

Para integrar o trader em aplicações Python siga a ordem abaixo:

1. Chame ``load_environment()`` para carregar variáveis de ambiente do
   ``.env`` ou do sistema.
2. Instancie ``QUALIARealTimeTrader`` informando um dicionário no
   parâmetro ``config``.
3. Execute ``await trader.initialize()`` antes de ``run`` ou
   ``run_once``.
4. Cada iteração de ``run`` chama ``TradingQASTCore.run_cycle`` que retorna as
   decisões de trading e métricas do ciclo.

A normalização dos símbolos é realizada logo após a conexão com a
exchange durante ``initialize``. Em seguida o sistema configura o QPM,
o universo quântico e o QMC usando esses símbolos normalizados.

O argumento ``strategy_config_path`` aponta para o JSON com os
parâmetros da estratégia. Caso seja omitido, o caminho é obtido da
variável ``QUALIA_STRATEGY_CONFIG``. Outras variáveis, como
``QUALIA_CACHE_DIR`` e ``QUALIA_LOGS_DIR``, também são carregadas por
``load_environment`` para definir os diretórios padrão.

Exemplo rápido:

```python
from src.qualia.qualia_trading_system import (
    QUALIARealTimeTrader,
    load_environment,
)

load_environment()
trader = QUALIARealTimeTrader(
    symbols=["BTC/USDT"],
    timeframes=["5m"],
    capital=10000,
    risk_profile="moderate",
    strategy_config_path="config/strategy_parameters.json",
    config={"risk_profile_settings": {"moderate": {...}}},
)
await trader.initialize()
await trader.run_once()
```

Para abrir manualmente uma posição em modo simulado usando uma ordem ``limit``
informe ``limit_price`` ao chamar ``_open_position``:

```python
await trader._open_position(
    symbol="BTC/USDT",
    side="buy",
    entry_price=50000.0,
    size=0.1,
    stop_loss=49500.0,
    take_profit=50500.0,
    q_signature_packet_arg=None,
    market_snapshot=None,
    decision_context=None,
    limit_price=49950.0,
)
```

Em execuções ao vivo o ``on_tick`` é disparado pelo ``QUALIARealTimeTrader`` a
cada ciclo de mercado, alimentando a HUD e o ``DynamicLogoEngine``. Consulte
[docs/hud.md](docs/hud.md) para detalhes da visualização e do armazenamento das métricas de FPS/latência.
Para o detalhamento de como as métricas mapeiam para os uniforms dos shaders, consulte [docs/holographic_metrics_mapping.md](docs/holographic_metrics_mapping.md).

Para habilitar métricas StatsD utilize um cliente ``DogStatsd``:

```python
from datadog import DogStatsd

statsd = DogStatsd()
trader = QUALIARealTimeTrader(
    symbols=["BTC/USDT"],
    timeframes=["5m"],
    capital=10000,
    risk_profile="moderate",
    statsd_client=statsd,
)
```

### Cálculo do histórico mínimo

Alguns parâmetros da ``QualiaTSVFStrategy`` definem o número mínimo de velas
necessárias para iniciar as análises. O método
``required_initial_data_length`` calcula esse valor considerando as três
subestratégias (S1, S2 e S3). Para S3, quando o fator de reamostragem é muito
alto (``resample_factor`` > ``60``), limitamos a janela a no máximo ``72``
candles para evitar valores irreais. Depois de computado o mínimo teórico,
aplicamos um teto prático de ``720`` candles.

```
min_len_s1 = s1_tsvf_window + 1
min_len_s2 = max(s2_sma_long_period, s2_rsi_period) + 1
resample_factor = pandas.Timedelta(s3_resample_period) / pandas.Timedelta(timeframe)
if resample_factor > 60:
    min_len_s3 = min(72, ceil(resample_factor * (s3_tsvf_window + 1)))
else:
    min_len_s3 = ceil(resample_factor * (s3_tsvf_window + 1))
raw = max(min_len_s1, min_len_s2, min_len_s3, 300)
required = min(raw, 720)
```

Com ``s3_tsvf_window=300`` e ``s3_resample_period="4h"`` em um timeframe de
``5m``, o fator de reamostragem é ``48``. ``min_len_s3`` seria ``336`` mas o
resultado final será ``720`` devido ao teto.

Ao carregar ``strategy_parameters.json`` o método ``_load_strategy_parameters``
verifica automaticamente esse valor. Se o ``preload`` configurado for menor do
que ``required_initial_data_length`` da estratégia, o sistema registra um erro e
ajusta o ``preload_candles_<timeframe>`` para o mínimo necessário antes de
continuar a inicialização.
Quando essa configuração estiver ausente, o sistema usa automaticamente o valor de `required_initial_data_length` calculado pela estratégia. Defina `preload_candles_<timeframe>` em `strategy_parameters.json` caso deseje sobrepor essa quantidade.

## Explorações Futuras

O QUALIA segue em constante evolução e algumas ideias conceituais estão sendo avaliadas. Entre elas destacam-se:

- **Retrocausal trading** – análise teórica de como sinais de mercado poderiam influenciar decisões passadas, em um modelo de feedback temporal.
- **Memória holográfica** – armazenamento distribuído de padrões de mercado inspirado em holografia quântica. O script `scripts/start_real_trading.py` persiste cada `QuantumSignaturePacket` para consulta futura.

Algumas funcionalidades continuam em fase de pesquisa. A memória holográfica possui implementação experimental e está integrada ao script de trading real. Consulte [docs/EXPLORACOES_CONCEITUAIS.md](docs/EXPLORACOES_CONCEITUAIS.md) para mais detalhes.

### Recursos Adicionais

- [docs/ARCHITECTURE_OVERVIEW.md](docs/ARCHITECTURE_OVERVIEW.md) apresenta um panorama dos módulos do projeto.
- Consulte o [CHANGELOG.md](CHANGELOG.md) para acompanhar as versões e alterações.
- [docs/api/metrics.md](docs/api/metrics.md) detalha o uso das métricas, o campo
  `max_history_size` e a função `cross_modal_coherence` empregada pelo NEXUS.
- [docs/api/temporal_detector.md](docs/api/temporal_detector.md) explica quando usar `MarketTemporalPatternDetector` (mercado) e `QuantumTemporalPatternDetector` (análises avançadas), além das métricas StatsD `temporal_detector.patterns` e `temporal_detector.execution_ms`.
- [docs/api/qast_evolution_metrics.md](docs/api/qast_evolution_metrics.md) explica as métricas da `QASTEvolutionaryStrategy`.
- [docs/api/qast_core.md](docs/api/qast_core.md) explica como `extract_signal_features` integra-se ao `QASTCore`.
- [docs/api/signal_generation_flow.md](docs/api/signal_generation_flow.md) descreve
  como `universe.py` e `qast_core.py` cooperam para gerar sinais de trading.
- [docs/api/simulation_core.md](docs/api/simulation_core.md) documenta `run_internal_reality_simulation` e o equilíbrio do universo interno.
- **Métricas e histórico** – as listas de métricas são controladas pelo parâmetro `max_history_size` do `QUALIAMetrics`. Ajuste esse valor para limitar a quantidade de registros mantidos e evite consumo excessivo de memória.
- Este projeto é distribuído sob a licença [MIT](LICENSE).

### Reproduzindo caso de baixa diversidade

O script `scripts/reproduce_low_diversity.py` executa o universo com cinco qubits e profundidade de scrambling reduzida. O resultado ilustra uma razão de diversidade de contagens abaixo do limite definido (0.1), gerando um aviso no log e um circuito de profundidade aproximada de 20 camadas.

Experimentos demonstraram que a baixa diversidade ocorre principalmente quando o circuito executa apenas um passo ou usa profundidade de scrambling mínima. Aumentar `scr_depth`, `qpu_steps` ou rodar ao menos dois passos geralmente produz diversidade acima de 0.1 mesmo com cinco qubits. Ajustes na `measure_frequency` tiveram impacto marginal. Desde a versão 0.1.2 é possível definir `measure_frequency` como `0` para habilitar **lazy measurement**, realizando medições apenas no passo final do circuito.

### Exemplo de configuração com alta diversidade

O script `scripts/reproduce_high_diversity.py` demonstra uma configuração mais robusta. Ele utiliza `scr_depth=6`, `qpu_steps=8`, habilita ruído térmico e executa **quatro** passos do universo. Essa combinação costuma produzir `counts_diversity_ratio` acima de `0.20` com seed fixo, gerando um circuito mais profundo e balanceado.

Para reproduzir exatamente este cenário, execute:

```bash
python scripts/reproduce_high_diversity.py --fixed-seed
```

A flag `--post-randomize-layers` controls how many randomization layers are
applied after trimming. The default value ``1`` ensures a balanced circuit,
while larger values may increase diversity at the cost of depth.

O arquivo `config/strategy_parameters.json` mantém `qpu_steps` com valor padrão 6. Para reproduções de alta diversidade acima de 0.20 recomenda-se aumentar para 8 conforme o exemplo acima.

Para orientações adicionais de ajuste de diversidade consulte [docs/diversity_tuning.md](docs/diversity_tuning.md).

### Execução opcional em hardware IBM Quantum

O repositório inclui o workflow `Hardware Smoke Test` que realiza uma execução
rápida em um backend real da IBM. Para habilitá-lo você deve definir o segredo
`IBMQ_TOKEN` nas configurações do GitHub:

1. Acesse **Settings → Secrets and variables → Actions** no repositório.
2. Clique em **New repository secret** e adicione `IBMQ_TOKEN` com o token
   obtido em [IBM Quantum](https://quantum.ibm.com/account).
3. Após salvar o segredo, o workflow rodará automaticamente em pull requests
   quando o token estiver presente.

## Executando Scripts de Demonstração

Para rodar os scripts de demonstração disponíveis em `scripts/`, primeiro instale o pacote em modo editável a partir da raiz do projeto:

```bash
pip install -e .[dev]
```

Esses demos dependem de bibliotecas opcionais. Instale-as com:

```bash
pip install librosa opencv-python qiskit-aer tqdm
```

Em seguida, execute o script passando os caminhos para o arquivo de áudio e para o vídeo:

```bash
python scripts/demo_synesthetic_fusion.py data/exemplo.wav data/exemplo.mp4
```

O processo gerará `fusion_brightness.npy` no mesmo diretório do áudio.
\nA meta de cobertura dos testes para o módulo de memória é de **80%**. Execute `pytest --cov=src/qualia/memory` para verificar.

## Backtesting

Execute o backtester para validar estratégias utilizando dados históricos.

```bash
python -m backtest.backtest_harness --config configs/sample.yaml \
  --historical_data_path data/btc_usdt.csv --symbol BTC/USDT --timeframe 1h \
  --output_report_path results/report.json
```

O arquivo `configs/sample.yaml` deve conter parâmetros da estratégia e perfis de risco. O relatório JSON será salvo no caminho indicado por `--output_report_path`, e um arquivo adicional com o histórico de trades será criado no mesmo diretório.
