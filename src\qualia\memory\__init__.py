"""QUALIA memory module with lazy imports to avoid circular dependencies."""

from __future__ import annotations

import importlib
from typing import Any

__all__ = [
    "BaseMemory",
    "ExperienceReplay",
    "ShortTermMemory",
    "QuantumPatternMemory",
    "HolographicMemory",
    "MemorySystem",
    "MemoryService",
    "with_lock",
    "SimilarityMixin",
    "WarmStartManager",
    "PatternPersistence",
    "IntentMemory",
    "flatten_vector",
    "load_qpm_vectors",
    "compute_similarity_distribution",
    "STORE_EVENT",
    "StorePatternEvent",
    "publish_store_pattern",
    "register_store_handler",
    "get_qpm_instance",
]

_MODULE_MAP = {
    "BaseMemory": ".base_memory",
    "ExperienceReplay": ".experience_replay",
    "ShortTermMemory": ".short_term_memory",
    "QuantumPatternMemory": ".quantum_pattern_memory",
    "HolographicMemory": ".holographic_memory",
    "MemorySystem": ".system",
    "MemoryService": ".service",
    "with_lock": ".locking",
    "SimilarityMixin": ".similarity",
    "WarmStartManager": ".warmstart",
    "PatternPersistence": ".persistence",
    "IntentMemory": ".intent_memory",
    "flatten_vector": ".qpm_utils",
    "load_qpm_vectors": ".qpm_utils",
    "compute_similarity_distribution": ".qpm_utils",
    "STORE_EVENT": ".qpm_interface",
    "StorePatternEvent": ".qpm_interface",
    "publish_store_pattern": ".qpm_interface",
    "register_store_handler": ".qpm_interface",
    "get_qpm_instance": ".qpm_loader",
}


def __getattr__(name: str) -> Any:
    """Lazily import submodules on first access."""
    module_path = _MODULE_MAP.get(name)
    if module_path is None:
        raise AttributeError(f"module 'qualia.memory' has no attribute {name}")
    
    if module_path.startswith('.'):
        module = importlib.import_module(module_path, __package__)
    else:
        module = importlib.import_module(module_path)
    return getattr(module, name)
