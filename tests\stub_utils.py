import sys
import types
from typing import Any
import numpy as np


def install_stubs():
    try:
        import pandas  # noqa: F401
    except Exception:  # pragma: no cover - optional dependency may be missing
        pandas = None

    if pandas is None and "pandas" not in sys.modules:
        pd_mod = types.ModuleType("pandas")

        class _Series(list):
            def __init__(self, data):
                super().__init__(data)

            def ewm(self, *a, **k):
                class _EWM:
                    def __init__(self, data):
                        self._data = data

                    def mean(self):
                        return self._data

                return _EWM(self)

            def to_numpy(self):
                return np.array(self)

        api_pkg = types.ModuleType("pandas.api")
        ext_pkg = types.ModuleType("pandas.api.extensions")
        ext_pkg.ExtensionArray = list
        api_pkg.extensions = ext_pkg

        pd_mod.Series = _Series

        class _DataFrame:
            def __init__(self, *a, **k):
                self.empty = False

            def to_numpy(self):
                return np.array([])

            def __len__(self):
                return 0

        pd_mod.DataFrame = _DataFrame
        pd_mod.Timestamp = lambda *a, **k: None
        pd_mod.api = api_pkg

        sys.modules["pandas"] = pd_mod
        sys.modules["pandas.api"] = api_pkg
        sys.modules["pandas.api.extensions"] = ext_pkg
    if "ccxt" not in sys.modules:
        ccxt_mod = types.ModuleType("ccxt")

        class _BaseExchange:
            def __call__(self, *a, **k):
                return self

            def load_markets(self):
                return {}

            def market(self, symbol):
                return {"limits": {"amount": {"min": None}}}

        ccxt_mod.Exchange = _BaseExchange

        def __getattr__(name):
            return _BaseExchange

        ccxt_mod.__getattr__ = __getattr__
        sys.modules["ccxt"] = ccxt_mod
        ccxt_async_mod = types.ModuleType("ccxt.async_support")
        ccxt_async_mod.Exchange = _BaseExchange
        ccxt_async_mod.__getattr__ = __getattr__
        sys.modules["ccxt.async_support"] = ccxt_async_mod
    if "src.qualia.utils.cache" not in sys.modules:
        cache_mod = types.ModuleType("cache")

        def _noop_cache(maxsize: int = 128):
            def decorator(func):
                return func

            return decorator

        def _simple_cache_key(obj):
            return hash(str(obj))

        class _DummyCacheManager:
            def __init__(self, *a, **k):
                self.store = {}

            def get(self, key):
                return self.store.get(key)

            def set(self, key, value):
                self.store[key] = value

        cache_mod.numpy_cache = _noop_cache
        cache_mod.get_cache_key = _simple_cache_key
        cache_mod.CacheManager = _DummyCacheManager
        cache_mod.get_cache_manager = lambda *a, **k: _DummyCacheManager()
        sys.modules["src.qualia.utils.cache"] = cache_mod
    if "src.qualia.utils.persistence" not in sys.modules:
        pers_mod = types.ModuleType("persistence")
        pers_mod.save_positions_json = lambda *a, **k: None
        pers_mod.load_positions_json = lambda *a, **k: {}
        pers_mod.convert_to_serializable = lambda obj: obj
        # YAA: Adiciona stubs mínimos para funções usadas durante os testes.
        pers_mod.load_json_safe = lambda *_a, **_k: {}
        pers_mod.save_json_safe = lambda *_a, **_k: None
        sys.modules["src.qualia.utils.persistence"] = pers_mod
    if "src.qualia.utils.display_utils" not in sys.modules:
        disp_mod = types.ModuleType("display_utils")
        for name in [
            "get_circuit_drawing",
            "format_statevector",
            "format_counts",
            "export_results_to_csv",
            "export_circuit_to_qasm",
        ]:
            setattr(disp_mod, name, lambda *a, **k: None)
        sys.modules["src.qualia.utils.display_utils"] = disp_mod
    if "src.qualia.utils.quantum_utils" not in sys.modules:
        q_mod = types.ModuleType("quantum_utils")
        # Basic stubs for quantum utility helpers used during tests. Each
        # function simply returns ``None`` or the input circuit. This avoids
        # import errors when modules reference optional quantum dependencies.
        for name in [
            "calculate_entropy",
            "clean_metrics_data",
            "parse_complex_string",
            "bitstring_to_int",
            "int_to_bitstring",
            "hamming_distance",
            "fidelity",
            "prepare_statevector",
            "sv_entropy",
            "linear_entropy",
            "validate_circuit_limits",
        ]:
            setattr(q_mod, name, lambda *a, **k: None)

        def enforce_circuit_limits(circuit, *a, **k):
            return circuit

        def measure_entropy(_sv):
            return {"sv_entropy": 0.0, "linear_entropy": 0.0}

        q_mod.enforce_circuit_limits = enforce_circuit_limits
        q_mod.measure_entropy = measure_entropy

        sys.modules["src.qualia.utils.quantum_utils"] = q_mod
    if "src.qualia.utils.signature_utils" not in sys.modules:
        sig_mod = types.ModuleType("signature_utils")
        sig_mod.expected_signature_dimension = lambda *a, **k: None
        sys.modules["src.qualia.utils.signature_utils"] = sig_mod
    if "pywt" not in sys.modules:
        pywt_mod = types.ModuleType("pywt")
        pywt_mod.wavedec = lambda *a, **k: []
        pywt_mod.waverec = lambda *a, **k: []
        sys.modules["pywt"] = pywt_mod
    if "qiskit_aer" not in sys.modules:
        from importlib.machinery import ModuleSpec

        aer_mod = types.ModuleType("qiskit_aer")
        aer_mod.__spec__ = ModuleSpec("qiskit_aer", loader=None, is_package=True)
        sys.modules["qiskit_aer"] = aer_mod
    if "qualia.market.kraken_integration" not in sys.modules:
        import importlib.util

        spec = importlib.util.find_spec("qualia.market")
        market_pkg = sys.modules.get("qualia.market")
        if spec is None and market_pkg is None:
            market_pkg = types.ModuleType("qualia.market")
            market_pkg.__path__ = []
            sys.modules["qualia.market"] = market_pkg
            if "qualia" in sys.modules:
                setattr(sys.modules["qualia"], "market", market_pkg)
        elif market_pkg is None:
            market_pkg = importlib.import_module("qualia.market")

        # Stub symbol utilities used during tests
        symbol_utils_mod = types.ModuleType("symbol_utils")
        symbol_utils_mod.validate_and_normalize_symbols = (
            lambda symbols, exch=None: list(symbols)
        )
        symbol_utils_mod.to_canonical_format = lambda symbol: symbol
        symbol_utils_mod.normalize_symbol = lambda symbol: symbol

        async def _normalize_symbol_async(
            symbol: str, exchange: Any | None = None
        ) -> str:
            return symbol

        symbol_utils_mod.normalize_symbol_async = _normalize_symbol_async

        sys.modules["qualia.market.symbol_utils"] = symbol_utils_mod
        market_pkg.symbol_utils = symbol_utils_mod
        ki_mod = types.ModuleType("qualia.market.kraken_integration")

        class KrakenIntegration:
            async def load_markets(self) -> dict:
                return {}

        def _load_exchange_credentials(**kwargs: Any) -> dict:
            return {
                "api_key": "k",
                "api_secret": "s",
                "password": kwargs.get("password"),
                "conn_timeout": kwargs.get("conn_timeout", 1),
                "conn_retries": kwargs.get("conn_retries", 1),
                "ticker_timeout": kwargs.get("ticker_timeout", 1),
                "ticker_retries": kwargs.get("ticker_retries", 1),
                "ticker_backoff_base": kwargs.get("ticker_backoff_base", 1.0),
            }

        ki_mod.KrakenIntegration = KrakenIntegration
        ki_mod._load_exchange_credentials = _load_exchange_credentials
        sys.modules["qualia.market.kraken_integration"] = ki_mod
        sys.modules["qualia.market"].kraken_integration = ki_mod
        # Stub base_integration module with minimal CryptoDataFetcher
        bi_mod = types.ModuleType("qualia.market.base_integration")

        class CryptoDataFetcher:
            def __init__(self, *a: Any, **k: Any) -> None:
                self.exchange_id = k.get("exchange_id", "stub")

            async def fetch_ticker(self, *_a: Any, **_k: Any) -> dict:
                return {}

        bi_mod.CryptoDataFetcher = CryptoDataFetcher
        sys.modules["qualia.market.base_integration"] = bi_mod
        sys.modules["qualia.market"].base_integration = bi_mod
        kc_mod = types.ModuleType("qualia.market.kucoin_integration")

        class KucoinIntegration:
            async def load_markets(self) -> dict:
                return {}

        kc_mod.KucoinIntegration = KucoinIntegration
        sys.modules["qualia.market.kucoin_integration"] = kc_mod
        sys.modules["qualia.market"].kucoin_integration = kc_mod

        bi_mod2 = types.ModuleType("qualia.market.binance_integration")

        class BinanceIntegration(CryptoDataFetcher):
            def __init__(self, *a: Any, **k: Any) -> None:
                super().__init__(*a, exchange_id="binance", **k)

        bi_mod2.BinanceIntegration = BinanceIntegration
        sys.modules["qualia.market.binance_integration"] = bi_mod2
        sys.modules["qualia.market"].binance_integration = bi_mod2

        cb_mod = types.ModuleType("qualia.market.coinbase_integration")

        class CoinbaseIntegration(CryptoDataFetcher):
            def __init__(self, *a: Any, **k: Any) -> None:
                super().__init__(*a, exchange_id="coinbase", **k)

        cb_mod.CoinbaseIntegration = CoinbaseIntegration
        sys.modules["qualia.market.coinbase_integration"] = cb_mod
        sys.modules["qualia.market"].coinbase_integration = cb_mod
        # DynamicRiskController não é stubbado para permitir testes completos
    if "psutil" not in sys.modules:
        sys.modules["psutil"] = types.ModuleType("psutil")
    if "aiopubsub" not in sys.modules:
        aio_mod = types.ModuleType("aiopubsub")

        class _Base:
            def __init__(self, *a, **k):
                pass

        class _Publisher(_Base):
            def publish(self, *_a, **_k):
                pass

        class _Subscriber(_Base):
            async def add_async_listener(self, *_a, **_k):
                pass

            async def remove_listener(self, *_a, **_k):
                pass

        aio_mod.Hub = _Base
        aio_mod.Key = _Base
        aio_mod.Publisher = _Publisher
        aio_mod.Subscriber = _Subscriber
        sys.modules["aiopubsub"] = aio_mod
    if "qualia.farsight.holographic_extension" not in sys.modules:
        hf_mod = types.ModuleType("qualia.farsight.holographic_extension")
        hf_mod.HolographicFarsightEngine = object
        sys.modules["qualia.farsight.holographic_extension"] = hf_mod
    if "qualia.farsight.analyzer" not in sys.modules:
        an_mod = types.ModuleType("qualia.farsight.analyzer")
        an_mod.FarsightEngine = object
        sys.modules["qualia.farsight.analyzer"] = an_mod
    if "qualia.consciousness.social_simulation_universe" not in sys.modules:
        ssu_mod = types.ModuleType("qualia.consciousness.social_simulation_universe")
        ssu_mod.SocialSimulationUniverse = object
        sys.modules["qualia.consciousness.social_simulation_universe"] = ssu_mod
    if "qualia.consciousness.enhanced_data_collector" not in sys.modules:
        edc_mod = types.ModuleType("qualia.consciousness.enhanced_data_collector")

        class EnhancedDataCollector:
            pass

        edc_mod.EnhancedDataCollector = EnhancedDataCollector
        sys.modules["qualia.consciousness.enhanced_data_collector"] = edc_mod
        if "qualia.consciousness" in sys.modules:
            sys.modules["qualia.consciousness"].enhanced_data_collector = edc_mod
    if "qualia.consciousness.holographic_universe" not in sys.modules:
        hu_mod = types.ModuleType("qualia.consciousness.holographic_universe")
        hu_mod.HolographicMarketUniverse = object
        hu_mod.HolographicEvent = object
        hu_mod.HolographicPattern = object
        hu_mod.TradingSignal = object
        sys.modules["qualia.consciousness.holographic_universe"] = hu_mod
    if "qualia.custom_types" not in sys.modules:
        ct_mod = types.ModuleType("qualia.custom_types")
        ct_mod.FutureProbabilityMap = object
        ct_mod.FutureScenario = object
        sys.modules["qualia.custom_types"] = ct_mod
