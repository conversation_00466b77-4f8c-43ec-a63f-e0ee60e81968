# BinanceIntegration

Módulo de integração com a exchange Binance. A implementação segue a mesma
estrutura da `KrakenIntegration` e oferece suporte à API REST e ao WebSocket
oficial.

Principais funcionalidades:

- Autenticação via API Key, Secret e Passphrase.
- Consulta de saldo e histórico de ordens utilizando a API REST via `ccxt`.
- Envio e cancelamento de ordens *market* e *limit*.
- Monitoramento de mercado em tempo real através de WebSocket usando o pacote
  `python-binance`.

Dependência adicional:

- [`python-binance`](https://pypi.org/project/python-binance/) para gerenciamento
  do WebSocket.

A classe principal reside em `src/qualia/market/binance_integration.py`. Falhas
de conexão registram o último erro encontrado para facilitar a depuração.

## Configuração

Defina as credenciais via variáveis de ambiente para evitar que chaves fiquem
expostas no código:

- `BINANCE_API_KEY`
- `BINANCE_SECRET_KEY`
- `BINANCE_PASSPHRASE`

Outros parâmetros como `EXCHANGE_CONN_TIMEOUT`, `EXCHANGE_CONN_RETRIES`,
`TICKER_TIMEOUT` e `TICKER_RETRY_ATTEMPTS` permitem ajustar tolerância a falhas e limites de
requisições conforme descrito no README.

## Segurança

O módulo utiliza o utilitário `SecurityManager` do projeto para opcionalmente
criptografar credenciais. Defina `QUALIA_SECRET_KEY` ou
`QUALIA_SECRET_KEY_PATH` para ativar o mecanismo de criptografia e reduzir o
risco de vazamento de chaves.

## Uso básico

```python
from src.qualia.market.binance_integration import BinanceIntegration

binance = BinanceIntegration(use_websocket=True)
await binance.initialize_connection()
ticker = await binance.fetch_ticker("BTC/USDT")
```

O método `initialize_connection` carrega os mercados e valida a conexão. Com
`use_websocket=True` é possível assinar canais de preço em tempo real.
