from __future__ import annotations

"""Audio-specific QuantumEncoders.

<PERSON><PERSON> módulo define encoders quânticos para representações de áudio. A classe
`MFCCQuantumEncoder` converte um vetor de 13 coeficientes MFCC em um estado
quântico via amplitude-encoding distribuído em 4 qubits (16 amplitudes).

Snapshot esperado::

    {
        "mfcc": np.ndarray shape (13,)  # opcional se `samples` fornecido
        "samples": np.ndarray           # waveform mono opcional
        "sample_rate": int              # taxa de amostragem (default 22050)
    }

Dependência opcional: ``librosa``. Se indisponível e os coeficientes MFCC não
forem providos, o encoder gera o estado base ``|0...0⟩``.
"""

from typing import Any, Dict, Sequence, List

import numpy as np

try:
    import librosa  # type: ignore
except (
    ModuleNotFoundError
):  # pragma: no cover – fallback quando librosa não está instalado
    librosa = None  # type: ignore

from ..config import encoder_registry as _er
from qualia.core.encoders import QuantumEncoder as _QE_base
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Use the same QuantumEncoder object referenced by encoder_registry to avoid duplicate import issues
QuantumEncoder = _er.QuantumEncoder

# ---------------------------------------------------------------------------
# MFCCQuantumEncoder
# ---------------------------------------------------------------------------


class MFCCQuantumEncoder(QuantumEncoder):
    """Codifica 13 coeficientes MFCC em 4 qubits (16 amplitudes).

    A estratégia: pad/trim do vetor MFCC para 16 valores, normalização 0-1, e
    amplitude-encoding (vetor normalizado L2 = 1). Ideal para pipelines que já
    calculam MFCC, mas também pode extrair internamente se ``librosa`` estiver
    instalado.
    """

    _DEFAULT_SR = 22050

    def __init__(
        self,
        n_mfcc: int = 13,
        sample_rate: int = _DEFAULT_SR,
        name: str = "MFCCQuantumEncoder",
    ) -> None:
        super().__init__(name)
        self.n_mfcc = int(n_mfcc)
        self.sample_rate = int(sample_rate)
        # 4 qubits ⇒ 16 amplitudes
        self.output_dim = 16
        logger.debug(
            "%s inicializado com n_mfcc=%d, sample_rate=%d",
            self.name,
            self.n_mfcc,
            self.sample_rate,
        )

    # ------------------------- Helpers ------------------------------------

    def _extract_mfcc(self, snap: Dict[str, Any]) -> np.ndarray:
        """Retorna vetor MFCC (length = ``self.n_mfcc``) a partir do *snapshot*.

        Prioridade:
        1. Se chave ``mfcc`` existir → usa diretamente.
        2. Caso contrário tenta calcular usando ``samples`` + ``sample_rate``
           com *librosa* se disponível.
        3. Fallback: vetor zeros.
        """
        if "mfcc" in snap:
            arr = np.asarray(snap["mfcc"], dtype=np.float32).flatten()
            if arr.size != self.n_mfcc:
                logger.warning(
                    "[%s] vetor MFCC de tamanho %d, esperado %d – ajustando via pad/trim.",
                    self.name,
                    arr.size,
                    self.n_mfcc,
                )
                if arr.size > self.n_mfcc:
                    arr = arr[: self.n_mfcc]
                else:
                    arr = np.pad(arr, (0, self.n_mfcc - arr.size))
            return arr

        if librosa is not None and "samples" in snap:
            samples = np.asarray(snap["samples"], dtype=np.float32)
            sr = int(snap.get("sample_rate", self.sample_rate))
            try:
                mfcc_feat = librosa.feature.mfcc(y=samples, sr=sr, n_mfcc=self.n_mfcc)
                # Média temporal para vetor fixo
                return mfcc_feat.mean(axis=1).astype(np.float32)
            except Exception as exc:  # pragma: no cover – erros de processamento
                logger.exception(
                    "[%s] erro ao extrair MFCC via librosa: %s", self.name, exc
                )

        # Fallback
        logger.error(
            "[%s] Nenhum MFCC ou samples fornecido – retornando zeros.", self.name
        )
        return np.zeros(self.n_mfcc, dtype=np.float32)

    # ---------------------- Encode ----------------------------------------

    def _encode_single(self, snap: Dict[str, Any]) -> np.ndarray:  # type: ignore[override]
        vec = self._extract_mfcc(snap)
        # Normalização para 0-1 antes da amplitude
        if vec.ptp() > 1e-9:
            vec = (vec - vec.min()) / (vec.ptp())
        # Pad / trim para 16
        if vec.size < self.output_dim:
            vec = np.pad(vec, (0, self.output_dim - vec.size))
        else:
            vec = vec[: self.output_dim]
        # Amplitude encoding – normaliza L2
        norm = float(np.linalg.norm(vec))
        if norm < 1e-9:
            out = np.zeros(self.output_dim, dtype=np.float32)
            out[0] = 1.0  # |0000⟩
            return out
        return (vec / norm).astype(np.float32)

    def _encode_batch(self, snaps: Sequence[Dict[str, Any]]) -> np.ndarray:  # type: ignore[override]
        return np.stack([self._encode_single(s) for s in snaps], axis=0).astype(
            np.float32
        )

    def get_quantum_operation(self, snap: Dict[str, Any]):  # type: ignore[override]
        vec = self._encode_single(snap)
        
        # YAA CORREÇÃO CRÍTICA: Verificar e garantir normalização adequada
        norm_squared = np.sum(vec ** 2)
        if not np.isclose(norm_squared, 1.0, atol=1e-10):
            if norm_squared > 1e-12:
                vec = vec / np.sqrt(norm_squared)
            else:
                # Fallback para estado base normalizado 
                vec = np.zeros(self.output_dim, dtype=np.float32)
                vec[0] = 1.0
                
        # Verificação final
        final_norm_squared = np.sum(vec ** 2)
        if not np.isclose(final_norm_squared, 1.0, atol=1e-10):
            logger.warning(
                f"MFCC encoder '{self.name}': normalização falhou "
                f"(norm²={final_norm_squared:.12f}), forçando renormalização"
            )
            vec = vec / np.sqrt(final_norm_squared)
            
        return ("initialize", vec.tolist(), list(range(4)))


# ---------------------------------------------------------------------------
# Registro
# ---------------------------------------------------------------------------

_er.register_encoder("quantum_mfcc", MFCCQuantumEncoder)
logger.info("Audio encoder registrado: quantum_mfcc")
