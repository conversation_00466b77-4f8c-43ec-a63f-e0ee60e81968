"""Orquestração de loops do QUALIA Trading System."""

from .loops import (
    _data_collection_loop,
    _holographic_evolution_loop,
    _decision_cycle_loop,
    _consciousness_loop,
    _execution_cycle_loop,
    _monitoring_loop,
    _safety_monitoring_loop,
)
from .binance_orchestrator import BinanceHolographicTradingOrchestrator

__all__ = [
    "_data_collection_loop",
    "_holographic_evolution_loop",
    "_decision_cycle_loop",
    "_consciousness_loop",
    "_execution_cycle_loop",
    "_monitoring_loop",
    "_safety_monitoring_loop",
    "BinanceHolographicTradingOrchestrator",
]
