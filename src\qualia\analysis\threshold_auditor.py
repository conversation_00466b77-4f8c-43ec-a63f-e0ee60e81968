"""
YAA: Sistema de auditoria de thresholds e pesos para QUALIA.

Este módulo implementa análise de distribuições de raw_score vs. decision
e otimização automática de parâmetros via grid search ou Bayesian optimization.
"""

from __future__ import annotations

import json
import time
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import pandas as pd
from scipy import stats

try:  # pragma: no cover - sklearn may be stubbed during tests
    from sklearn.model_selection import ParameterGrid
    from sklearn.metrics import classification_report, confusion_matrix
except Exception:  # pragma: no cover - provide minimal stubs
    ParameterGrid = lambda params: [params]  # type: ignore[var-annotated]

    def classification_report(*_a: Any, **_k: Any) -> str:
        return ""

    def confusion_matrix(*_a: Any, **_k: Any) -> list[list[int]]:
        return [[0]]


from ..utils.logger import get_logger
from ..utils.statsd_client import get_statsd_client

logger = get_logger(__name__)


@dataclass
class DecisionRecord:
    """Registro de uma decisão do oráculo para análise."""

    timestamp: float
    symbol: str
    raw_score: float
    confidence: float
    threshold: float
    action: str
    features_on: List[str]
    reasoning: List[str]
    coherence: float
    strategy_weight: float
    holographic_weight: float
    metacognition_weight: float
    market_volatility: float


@dataclass
class ThresholdAnalysis:
    """Resultado da análise de thresholds."""

    current_threshold: float
    optimal_threshold: float
    precision: float
    recall: float
    f1_score: float
    accuracy: float
    false_positive_rate: float
    false_negative_rate: float
    score_distribution: Dict[str, float]


@dataclass
class WeightOptimizationResult:
    """Resultado da otimização de pesos."""

    original_weights: Dict[str, float]
    optimal_weights: Dict[str, float]
    performance_improvement: float
    backtest_results: Dict[str, Any]
    confidence_interval: Tuple[float, float]


class ThresholdAuditor:
    """
    Auditor de thresholds e pesos do sistema QUALIA.

    Funcionalidades:
    - Coleta e análise de distribuições de raw_score vs. decision
    - Otimização automática de thresholds
    - Grid search e Bayesian optimization para pesos
    - Backtesting de configurações otimizadas
    """

    def __init__(
        self, data_path: str = "data/analysis", statsd_client: Optional[Any] = None
    ):
        self.data_path = Path(data_path)
        self.data_path.mkdir(parents=True, exist_ok=True)
        self.statsd = statsd_client or get_statsd_client()

        # Arquivos de dados
        self.decisions_file = self.data_path / "decision_records.jsonl"
        self.analysis_file = self.data_path / "threshold_analysis.json"
        self.optimization_file = self.data_path / "weight_optimization.json"

        # Cache de dados
        self._decision_records: List[DecisionRecord] = []
        self._last_load_time = 0.0

    def record_decision(
        self,
        symbol: str,
        raw_score: float,
        confidence: float,
        threshold: float,
        action: str,
        features_on: List[str],
        reasoning: List[str],
        coherence: float,
        weights: Dict[str, float],
        market_volatility: float = 0.0,
    ) -> None:
        """Registra uma decisão para análise posterior."""
        record = DecisionRecord(
            timestamp=time.time(),
            symbol=symbol,
            raw_score=raw_score,
            confidence=confidence,
            threshold=threshold,
            action=action,
            features_on=features_on.copy(),
            reasoning=reasoning.copy(),
            coherence=coherence,
            strategy_weight=weights.get("strategy", 0.5),
            holographic_weight=weights.get("holographic", 0.3),
            metacognition_weight=weights.get("metacognition", 0.2),
            market_volatility=market_volatility,
        )

        # Salva no arquivo JSONL
        with open(self.decisions_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(asdict(record)) + "\n")

        # Adiciona ao cache
        self._decision_records.append(record)

        # Emite métricas
        if self.statsd:
            tags = [f"symbol:{symbol}", f"action:{action.lower()}"]
            self.statsd.gauge("auditor.raw_score", raw_score, tags=tags)
            self.statsd.gauge("auditor.confidence", confidence, tags=tags)
            self.statsd.gauge("auditor.threshold", threshold, tags=tags)
            self.statsd.gauge(
                "auditor.score_vs_threshold", raw_score - threshold, tags=tags
            )

    def load_decision_records(
        self, max_age_hours: float = 24.0
    ) -> List[DecisionRecord]:
        """Carrega registros de decisão do arquivo."""
        if not self.decisions_file.exists():
            return []

        # Verifica se precisa recarregar
        current_time = time.time()
        if (current_time - self._last_load_time) < 300:  # Cache por 5 minutos
            return self._decision_records

        records = []
        cutoff_time = current_time - (max_age_hours * 3600)

        try:
            with open(self.decisions_file, "r", encoding="utf-8") as f:
                for line in f:
                    if line.strip():
                        data = json.loads(line)
                        if data["timestamp"] >= cutoff_time:
                            records.append(DecisionRecord(**data))

        except Exception as e:
            logger.error(f"Erro carregando registros de decisão: {e}")
            return self._decision_records

        self._decision_records = records
        self._last_load_time = current_time
        logger.info(f"Carregados {len(records)} registros de decisão")

        return records

    def analyze_score_distributions(
        self, records: Optional[List[DecisionRecord]] = None
    ) -> Dict[str, Any]:
        """Analisa distribuições de raw_score por ação."""
        if records is None:
            records = self.load_decision_records()

        if not records:
            logger.warning("Nenhum registro disponível para análise")
            return {}

        # Converte para DataFrame
        df = pd.DataFrame([asdict(r) for r in records])

        analysis = {
            "total_decisions": len(df),
            "actions_distribution": df["action"].value_counts().to_dict(),
            "score_statistics": {},
            "threshold_analysis": {},
            "feature_importance": {},
            "timestamp": time.time(),
        }

        # Estatísticas por ação
        for action in df["action"].unique():
            action_df = df[df["action"] == action]
            scores = action_df["raw_score"]

            analysis["score_statistics"][action] = {
                "count": len(scores),
                "mean": float(scores.mean()),
                "std": float(scores.std()),
                "min": float(scores.min()),
                "max": float(scores.max()),
                "percentiles": {
                    "25": float(scores.quantile(0.25)),
                    "50": float(scores.quantile(0.50)),
                    "75": float(scores.quantile(0.75)),
                    "90": float(scores.quantile(0.90)),
                    "95": float(scores.quantile(0.95)),
                },
            }

        # Análise de threshold atual
        buy_sell_df = df[df["action"].isin(["BUY", "SELL"])]
        if len(buy_sell_df) > 0:
            current_threshold = buy_sell_df["threshold"].mean()
            scores_above_threshold = (
                buy_sell_df["raw_score"] >= buy_sell_df["threshold"]
            ).sum()

            analysis["threshold_analysis"] = {
                "current_avg_threshold": float(current_threshold),
                "decisions_above_threshold": int(scores_above_threshold),
                "decisions_below_threshold": int(
                    len(buy_sell_df) - scores_above_threshold
                ),
                "threshold_effectiveness": float(
                    scores_above_threshold / len(buy_sell_df)
                ),
            }

        # Análise de features mais ativas
        all_features = []
        for record in records:
            all_features.extend(record.features_on)

        if all_features:
            feature_counts = pd.Series(all_features).value_counts()
            analysis["feature_importance"] = feature_counts.head(10).to_dict()

        # Salva análise
        with open(self.analysis_file, "w", encoding="utf-8") as f:
            json.dump(analysis, f, indent=2)

        logger.info(f"Análise de distribuições concluída: {len(records)} registros")
        return analysis

    def optimize_threshold(
        self,
        records: Optional[List[DecisionRecord]] = None,
        target_metric: str = "f1_score",
    ) -> ThresholdAnalysis:
        """Otimiza threshold baseado em dados históricos."""
        if records is None:
            records = self.load_decision_records()

        if len(records) < 50:
            logger.warning("Dados insuficientes para otimização de threshold")
            return None

        # Converte para DataFrame
        df = pd.DataFrame([asdict(r) for r in records])

        # Filtra apenas decisões BUY/SELL para análise
        trade_df = df[df["action"].isin(["BUY", "SELL"])].copy()
        if len(trade_df) < 20:
            logger.warning("Dados de trade insuficientes para otimização")
            return None

        # Cria labels binários (1 para BUY/SELL, 0 para HOLD)
        trade_df["should_trade"] = 1
        hold_df = df[df["action"] == "HOLD"].copy()
        hold_df["should_trade"] = 0

        combined_df = pd.concat([trade_df, hold_df])

        # Testa diferentes thresholds
        thresholds = np.linspace(0.1, 0.8, 50)
        best_threshold = None
        best_score = -1
        results = []

        for threshold in thresholds:
            # Predições baseadas no threshold
            predictions = (combined_df["raw_score"] >= threshold).astype(int)
            actual = combined_df["should_trade"]

            # Calcula métricas
            tp = ((predictions == 1) & (actual == 1)).sum()
            fp = ((predictions == 1) & (actual == 0)).sum()
            tn = ((predictions == 0) & (actual == 0)).sum()
            fn = ((predictions == 0) & (actual == 1)).sum()

            if tp + fp > 0:
                precision = tp / (tp + fp)
            else:
                precision = 0

            if tp + fn > 0:
                recall = tp / (tp + fn)
            else:
                recall = 0

            if precision + recall > 0:
                f1 = 2 * (precision * recall) / (precision + recall)
            else:
                f1 = 0

            accuracy = (tp + tn) / len(combined_df)

            results.append(
                {
                    "threshold": threshold,
                    "precision": precision,
                    "recall": recall,
                    "f1_score": f1,
                    "accuracy": accuracy,
                    "tp": tp,
                    "fp": fp,
                    "tn": tn,
                    "fn": fn,
                }
            )

            # Verifica se é o melhor
            current_score = results[-1][target_metric]
            if current_score > best_score:
                best_score = current_score
                best_threshold = threshold

        # Encontra resultado ótimo
        best_result = next(r for r in results if r["threshold"] == best_threshold)
        current_threshold = combined_df["threshold"].mean()

        # Análise de distribuição de scores
        score_dist = {
            "mean": float(combined_df["raw_score"].mean()),
            "std": float(combined_df["raw_score"].std()),
            "trade_scores_mean": float(trade_df["raw_score"].mean()),
            "hold_scores_mean": (
                float(hold_df["raw_score"].mean()) if len(hold_df) > 0 else 0.0
            ),
        }

        analysis = ThresholdAnalysis(
            current_threshold=float(current_threshold),
            optimal_threshold=float(best_threshold),
            precision=best_result["precision"],
            recall=best_result["recall"],
            f1_score=best_result["f1_score"],
            accuracy=best_result["accuracy"],
            false_positive_rate=(
                best_result["fp"] / (best_result["fp"] + best_result["tn"])
                if (best_result["fp"] + best_result["tn"]) > 0
                else 0
            ),
            false_negative_rate=(
                best_result["fn"] / (best_result["fn"] + best_result["tp"])
                if (best_result["fn"] + best_result["tp"]) > 0
                else 0
            ),
            score_distribution=score_dist,
        )

        logger.info(
            f"Threshold otimizado: {current_threshold:.3f} -> {best_threshold:.3f} "
            f"(F1: {best_result['f1_score']:.3f})"
        )

        return analysis

    def grid_search_weights(
        self,
        records: Optional[List[DecisionRecord]] = None,
        param_grid: Optional[Dict[str, List[float]]] = None,
    ) -> WeightOptimizationResult:
        """Executa grid search para otimizar pesos de unificação."""
        if records is None:
            records = self.load_decision_records()

        if len(records) < 100:
            logger.warning("Dados insuficientes para otimização de pesos")
            return None

        # Grid padrão se não fornecido
        if param_grid is None:
            param_grid = {
                "strategy": [0.2, 0.3, 0.4, 0.5, 0.6],
                "holographic": [0.2, 0.3, 0.4, 0.5, 0.6],
                "metacognition": [0.1, 0.2, 0.3, 0.4],
            }

        # Gera todas as combinações que somam ~1.0
        valid_combinations = []
        for params in ParameterGrid(param_grid):
            total = sum(params.values())
            if 0.95 <= total <= 1.05:  # Tolerância para soma = 1
                # Normaliza para somar exatamente 1.0
                normalized = {k: v / total for k, v in params.items()}
                valid_combinations.append(normalized)

        logger.info(f"Testando {len(valid_combinations)} combinações de pesos")

        # Pesos originais (média dos registros)
        df = pd.DataFrame([asdict(r) for r in records])
        original_weights = {
            "strategy": df["strategy_weight"].mean(),
            "holographic": df["holographic_weight"].mean(),
            "metacognition": df["metacognition_weight"].mean(),
        }

        # Simula performance para cada combinação
        best_combination = None
        best_performance = -1
        results = []

        for weights in valid_combinations:
            # Simula decisões com novos pesos
            performance = self._simulate_weight_performance(records, weights)
            results.append({"weights": weights.copy(), "performance": performance})

            if performance > best_performance:
                best_performance = performance
                best_combination = weights

        # Calcula melhoria
        original_performance = self._simulate_weight_performance(
            records, original_weights
        )
        improvement = best_performance - original_performance

        # Intervalo de confiança (bootstrap simples)
        bootstrap_scores = []
        for _ in range(100):
            sample_records = np.random.choice(
                records, size=len(records) // 2, replace=True
            )
            score = self._simulate_weight_performance(sample_records, best_combination)
            bootstrap_scores.append(score)

        confidence_interval = (
            np.percentile(bootstrap_scores, 2.5),
            np.percentile(bootstrap_scores, 97.5),
        )

        result = WeightOptimizationResult(
            original_weights=original_weights,
            optimal_weights=best_combination,
            performance_improvement=improvement,
            backtest_results={
                "original_performance": original_performance,
                "optimal_performance": best_performance,
                "tested_combinations": len(valid_combinations),
                "all_results": results[:10],  # Top 10 apenas
            },
            confidence_interval=confidence_interval,
        )

        # Salva resultado
        with open(self.optimization_file, "w", encoding="utf-8") as f:
            json.dump(asdict(result), f, indent=2)

        logger.info(
            f"Otimização concluída. Melhoria: {improvement:.3f} "
            f"(CI: {confidence_interval[0]:.3f}-{confidence_interval[1]:.3f})"
        )

        return result

    def _simulate_weight_performance(
        self, records: List[DecisionRecord], weights: Dict[str, float]
    ) -> float:
        """Simula performance com novos pesos."""
        # Métrica simples: correlação entre confiança e sucesso
        # Em um sistema real, isso seria um backtest completo

        df = pd.DataFrame([asdict(r) for r in records])

        # Recalcula scores com novos pesos
        df["new_score"] = (
            df["raw_score"] * 0.4  # Base score
            + df["coherence"] * weights["strategy"] * 0.3
            + df["confidence"] * weights["holographic"] * 0.2
            + df["market_volatility"] * weights["metacognition"] * 0.1
        )

        # Métrica de performance: precisão das decisões de trade
        trade_df = df[df["action"].isin(["BUY", "SELL"])]
        if len(trade_df) == 0:
            return 0.0

        # Simula "sucesso" baseado em alta confiança + baixa volatilidade
        trade_df["simulated_success"] = (
            (trade_df["confidence"] > 0.6) & (trade_df["market_volatility"] < 0.05)
        ).astype(int)

        # Performance = taxa de sucesso ponderada pela nova pontuação
        if len(trade_df) > 0:
            weighted_success = (
                trade_df["simulated_success"] * trade_df["new_score"]
            ).sum()
            total_weight = trade_df["new_score"].sum()
            return weighted_success / total_weight if total_weight > 0 else 0.0

        return 0.0

    def generate_report(self, output_file: Optional[str] = None) -> Dict[str, Any]:
        """Gera relatório completo de auditoria."""
        records = self.load_decision_records()

        if not records:
            logger.warning("Nenhum dado disponível para relatório")
            return {}

        # Executa todas as análises
        score_analysis = self.analyze_score_distributions(records)
        threshold_analysis = self.optimize_threshold(records)
        weight_optimization = self.grid_search_weights(records)

        # Compila relatório
        report = {
            "metadata": {
                "generated_at": time.time(),
                "total_records": len(records),
                "analysis_period_hours": 24.0,
                "qualia_version": "2.0.0",
            },
            "score_distributions": score_analysis,
            "threshold_optimization": (
                asdict(threshold_analysis) if threshold_analysis else None
            ),
            "weight_optimization": (
                asdict(weight_optimization) if weight_optimization else None
            ),
            "recommendations": self._generate_recommendations(
                score_analysis, threshold_analysis, weight_optimization
            ),
        }

        # Salva relatório se solicitado
        if output_file:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2)
            logger.info(f"Relatório salvo em: {output_path}")

        return report

    def _generate_recommendations(
        self,
        score_analysis: Dict[str, Any],
        threshold_analysis: Optional[ThresholdAnalysis],
        weight_optimization: Optional[WeightOptimizationResult],
    ) -> List[Dict[str, Any]]:
        """Gera recomendações baseadas na análise."""
        recommendations = []

        # Recomendações de threshold
        if threshold_analysis:
            current_th = threshold_analysis.current_threshold
            optimal_th = threshold_analysis.optimal_threshold

            if abs(optimal_th - current_th) > 0.05:
                recommendations.append(
                    {
                        "type": "threshold_adjustment",
                        "priority": "high",
                        "current_value": current_th,
                        "recommended_value": optimal_th,
                        "expected_improvement": f"F1-Score: {threshold_analysis.f1_score:.3f}",
                        "description": f"Ajustar decision_threshold de {current_th:.3f} para {optimal_th:.3f}",
                    }
                )

        # Recomendações de pesos
        if weight_optimization and weight_optimization.performance_improvement > 0.02:
            recommendations.append(
                {
                    "type": "weight_optimization",
                    "priority": "medium",
                    "current_weights": weight_optimization.original_weights,
                    "recommended_weights": weight_optimization.optimal_weights,
                    "expected_improvement": f"Performance: +{weight_optimization.performance_improvement:.3f}",
                    "description": "Ajustar pesos de unificação conforme otimização",
                }
            )

        # Recomendações baseadas em distribuições
        if score_analysis.get("actions_distribution", {}).get("HOLD", 0) > 0.8:
            recommendations.append(
                {
                    "type": "threshold_reduction",
                    "priority": "medium",
                    "description": "Muitas decisões HOLD - considerar reduzir thresholds",
                    "current_hold_rate": score_analysis["actions_distribution"]["HOLD"],
                }
            )

        return recommendations
