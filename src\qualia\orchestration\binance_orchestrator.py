"""Real-time orchestrator para trading holográfico usando Binance."""

from __future__ import annotations

import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, Optional

import yaml

from ..consciousness.holographic_universe import HolographicMarketUniverse
from ..consciousness.real_data_collectors import RealDataCollector
from ..consciousness.holographic_trading_bridge import HolographicTradingBridge
from ..risk_management.advanced_risk_manager import AdvancedRiskManager
from ..exchanges.binance_client import BinanceClient
from ..utils.logger import get_logger

logger = get_logger(__name__)


class BinanceHolographicTradingOrchestrator:
    """Coordena coleta de dados, evolução holográfica e execução via Binance."""

    def __init__(self, config_path: str) -> None:
        self.config_path = config_path
        self.config = self._load_config()
        self.data_collector: Optional[RealDataCollector] = None
        self.holographic_universe: Optional[HolographicMarketUniverse] = None
        self.risk_manager: Optional[AdvancedRiskManager] = None
        self.binance_client: Optional[BinanceClient] = None
        self.trading_bridge: Optional[HolographicTradingBridge] = None
        self.running: bool = False
        self.performance_stats: Dict[str, Any] = {
            "start_time": None,
            "signals_generated": 0,
            "trades_executed": 0,
        }

    def _load_config(self) -> Dict[str, Any]:
        """Carrega configuração do arquivo YAML e aplica variáveis de ambiente."""
        with open(self.config_path, "r", encoding="utf-8") as f:
            loaded = yaml.safe_load(f)
        conf = loaded.get("holographic_trading", loaded)
        bin_cfg = conf.get("exchanges", {}).get("binance", {})
        bin_cfg["api_key"] = os.getenv("BINANCE_API_KEY", bin_cfg.get("api_key", ""))
        bin_cfg["api_secret"] = os.getenv(
            "BINANCE_API_SECRET", bin_cfg.get("api_secret", "")
        )
        conf.setdefault("exchanges", {})["binance"] = bin_cfg
        return conf

    async def initialize(self) -> None:
        """Inicializa componentes principais."""
        logger.info("🚀 Inicializando Holographic Trading (Binance)...")

        self.data_collector = RealDataCollector()
        await self.data_collector.__aenter__()

        holo_cfg = self.config.get("holographic", {})
        
        # Configuração de sinais de trading
        trading_signals_config = holo_cfg.get("trading_signals", {
            "proximity_threshold": 30,
            "confidence_threshold": 0.6,
            "min_strength": 0.7,
        })
        
        self.holographic_universe = HolographicMarketUniverse(
            field_size=tuple(holo_cfg.get("field_size", (200, 200))),
            time_steps=holo_cfg.get("time_steps", 500),
            diffusion_rate=holo_cfg.get("diffusion_rate", 0.25),
            feedback_strength=holo_cfg.get("feedback_strength", 0.05),
            min_history_length=holo_cfg.get("min_history_length", 20),
            trading_signals_config=trading_signals_config,
        )

        risk_cfg = self.config.get("risk", {})
        cap_cfg = self.config.get("capital", {})
        self.risk_manager = AdvancedRiskManager(
            initial_capital=cap_cfg.get("initial", 10000.0),
            risk_per_trade_pct=risk_cfg.get("per_trade_pct", 1.0),
            max_drawdown_pct=risk_cfg.get("max_drawdown_pct", 20.0),
            max_volatility=risk_cfg.get("max_volatility", 5.0),
        )

        bin_cfg = self.config.get("exchanges", {}).get("binance", {})
        if bin_cfg.get("api_key") and bin_cfg.get("api_secret"):
            self.binance_client = BinanceClient(bin_cfg)
            await self.binance_client.initialize()
            logger.info("✅ Binance client conectado")
        else:
            logger.warning("⚠️ Binance credentials não configuradas - modo paper")

        trading_cfg = self.config.get("trading", {})
        if self.binance_client:
            self.trading_bridge = HolographicTradingBridge(
                risk_manager=self.risk_manager,
                exchange_client=self.binance_client,
                config=trading_cfg,
            )
            logger.info("✅ Trading bridge ativo")
        else:
            logger.info("⚠️ Trading bridge não inicializado (modo paper)")

        self.performance_stats["start_time"] = asyncio.get_event_loop().time()
        logger.info("✅ Componentes prontos")

    async def run_trading_loop(self) -> None:
        """Loop principal de trading."""
        if not all([self.data_collector, self.holographic_universe]):
            raise RuntimeError("Orchestrator não inicializado")

        self.running = True
        timing = self.config.get("timing", {})
        last_collect = 0.0
        last_evolve = 0.0
        last_signals = 0.0
        last_metrics = 0.0
        last_positions = 0.0

        while self.running:
            now = asyncio.get_event_loop().time()
            if now - last_collect >= timing.get("data_collection_interval", 30):
                await self._collect_and_inject_data()
                last_collect = now
            if now - last_evolve >= timing.get("universe_evolution_interval", 10):
                await self._evolve_universe()
                last_evolve = now
            if now - last_signals >= timing.get("signal_generation_interval", 15):
                await self._generate_signals()
                last_signals = now
            if (
                self.trading_bridge
                and now - last_positions
                >= timing.get("position_monitoring_interval", 60)
            ):
                await self.trading_bridge.monitor_active_positions()
                last_positions = now
            if now - last_metrics >= timing.get("performance_update_interval", 60):
                await self._update_metrics()
                last_metrics = now
            await asyncio.sleep(1)

    async def _collect_and_inject_data(self) -> None:
        market = await self.data_collector.collect_market_data()
        news = await self.data_collector.collect_news_events()
        events = self.data_collector.convert_to_holographic_events(
            market, news, self.holographic_universe.field_size
        )
        for event in events:
            await self.holographic_universe.inject_holographic_event(event)

    async def _evolve_universe(self) -> None:
        await self.holographic_universe.step_evolution(time.time())

    async def _generate_signals(self) -> None:
        patterns = self.holographic_universe.analyze_holographic_patterns()
        signals = self.holographic_universe.generate_trading_signals(patterns)
        self.performance_stats["signals_generated"] += len(signals)
        for sig in signals:
            logger.info(
                "Sinal %s %s força=%.2f", sig.symbol, sig.action, sig.strength
            )
            if self.trading_bridge:
                result = await self.trading_bridge.process_holographic_signal(sig)
                if result and result.executed:
                    self.performance_stats["trades_executed"] += 1

    async def _update_metrics(self) -> None:
        uptime = asyncio.get_event_loop().time() - self.performance_stats["start_time"]
        summary = self.holographic_universe.get_field_summary()
        bridge_summary = (
            self.trading_bridge.get_performance_summary()
            if self.trading_bridge
            else {}
        )
        logger.info(
            "📊 PERFORMANCE: uptime=%ds signals=%d trades=%d field_energy=%.2f",
            int(uptime),
            self.performance_stats["signals_generated"],
            bridge_summary.get("executed_signals", 0),
            summary.get("total_energy", 0.0),
        )

    async def shutdown(self) -> None:
        """Encerra componentes de forma graciosa."""
        self.running = False
        if self.data_collector:
            self.data_collector.stop_collection()
            await self.data_collector.__aexit__(None, None, None)
        if self.holographic_universe:
            await self.holographic_universe.shutdown()
        if self.binance_client:
            await self.binance_client.shutdown()
        logger.info("✅ Shutdown completo")
