"""Folding Operator - Dimensional reduction and pattern compression.

Implements quantum-inspired dimensional folding for market data analysis.

"""

import numpy as np
from typing import List, Dict, Any, Optional

from ..utils.numba_utils import optional_njit
from dataclasses import dataclass
import logging
import os
from scipy.linalg import expm
from ..config.settings import folding_alpha

logger = logging.getLogger(__name__)


@dataclass
class FoldingState:
    """State representation for folding operations"""

    dimensions: int
    phase: float
    coherence: float
    folded_data: np.ndarray
    timestamp: float


class FoldingOperator:
    """
    Quantum-inspired folding operator for dimensional reduction

    Implements non-linear dimensional folding that preserves essential
    market structure while reducing complexity for pattern recognition.
    """

    def __init__(
        self,
        config: Dict[str, Any],
        hamiltonian: np.ndarray | None = None,
        *,
        history_maxlen: int = 1000,
    ) -> None:
        self.dimensions = config.get("dimensions", 7)
        self.coherence_threshold = config.get("coherence_threshold", 0.55)
        self.phase_coupling = config.get("phase_coupling", 0.382)
        self.alpha = float(config.get("alpha", folding_alpha))

        h_conf = config.get("hamiltonian")
        if hamiltonian is not None:
            self.hamiltonian = np.array(hamiltonian, dtype=float)
        elif h_conf is not None:
            self.hamiltonian = np.array(h_conf, dtype=float)
        else:
            self.hamiltonian = np.eye(self.dimensions)

        if self.hamiltonian.shape != (self.dimensions, self.dimensions):
            raise ValueError("hamiltonian must match operator dimensions")

        # Cached unitary matrices will be initialized in _initialize_folding_matrices
        self._unitary: np.ndarray | None = None
        self._unitary_inv: np.ndarray | None = None

        # Initialize folding matrices
        self._initialize_folding_matrices()

        # State tracking
        self.current_state: Optional[FoldingState] = None
        self.folding_history: List[FoldingState] = []
        self.history_maxlen = int(history_maxlen)

        logger.info(f"FoldingOperator initialized with {self.dimensions} dimensions")

    def _initialize_folding_matrices(self) -> None:
        """Initialize quantum-inspired folding transformation matrices"""
        # Primary folding matrix with golden ratio proportions
        self.primary_matrix = np.random.randn(self.dimensions, self.dimensions)

        # Apply golden ratio scaling using vectorized operations
        indices = np.arange(self.dimensions)
        scaling = self.coherence_threshold ** np.abs(
            indices[:, None] - indices[None, :]
        )
        np.fill_diagonal(scaling, 1.0)
        self.primary_matrix *= scaling

        # Normalize to maintain quantum unitarity constraints
        U, _, Vt = np.linalg.svd(self.primary_matrix)
        self.primary_matrix = U @ Vt

        # Phase coupling matrix
        self.phase_matrix = np.eye(self.dimensions)
        idx = np.arange(self.dimensions - 1)
        self.phase_matrix[idx, idx + 1] = self.phase_coupling
        self.phase_matrix[idx + 1, idx] = -self.phase_coupling

        # Precompute unitary evolution matrices to avoid repeated expm calls
        self._unitary = expm(-self.alpha * self.hamiltonian)
        self._unitary_inv = expm(self.alpha * self.hamiltonian)

    async def fold(self, market_data: np.ndarray, timestamp: float) -> FoldingState:
        """Apply folding transformation to market data.

        Parameters
        ----------
        market_data : np.ndarray
            Input market data array.
        timestamp : float
            Current timestamp.

        Returns
        -------
        FoldingState
            Transformed folding state.
        """
        try:
            # Ensure data has correct dimensions
            if len(market_data.shape) == 1:
                market_data = market_data.reshape(-1, 1)

            # Pad or truncate to match folding dimensions
            if market_data.shape[1] < self.dimensions:
                padding = np.zeros(
                    (market_data.shape[0], self.dimensions - market_data.shape[1])
                )
                market_data = np.hstack([market_data, padding])
            elif market_data.shape[1] > self.dimensions:
                market_data = market_data[:, : self.dimensions]

            # Apply cached unitary evolution generated from the Hamiltonian
            folded = market_data @ self._unitary

            # Apply phase coupling
            phase_component = folded @ self.phase_matrix

            # Combine with coherence weighting
            final_folded = folded * self.coherence_threshold + phase_component * (
                1 - self.coherence_threshold
            )

            # Calculate coherence measure
            coherence = self._calculate_coherence(folded, phase_component)

            # Calculate phase
            phase = np.angle(np.sum(folded * (1 + 1j * phase_component)))

            # Create folding state
            state = FoldingState(
                dimensions=self.dimensions,
                phase=phase,
                coherence=coherence,
                folded_data=final_folded,
                timestamp=timestamp,
            )

            self.current_state = state
            self.folding_history.append(state)

            # Limit history size
            if len(self.folding_history) > self.history_maxlen:
                self.folding_history.pop(0)

            logger.debug(
                f"Folding complete: coherence={coherence:.3f}, phase={phase:.3f}"
            )

            return state

        except Exception as e:
            logger.error(f"Error in folding operation: {e}")
            raise

    @staticmethod
    @optional_njit(cache=True)
    def _calculate_coherence_impl(
        folded: np.ndarray, phase_component: np.ndarray
    ) -> float:
        folded_norm = folded / (np.linalg.norm(folded, axis=1, keepdims=True) + 1e-8)
        phase_norm = phase_component / (
            np.linalg.norm(phase_component, axis=1, keepdims=True) + 1e-8
        )
        correlation = np.mean(np.sum(folded_norm * phase_norm, axis=1))
        coherence = (correlation + 1) / 2
        return np.clip(coherence, 0.0, 1.0)

    def _calculate_coherence(
        self, folded: np.ndarray, phase_component: np.ndarray
    ) -> float:
        """Calculate coherence measure between folded and phase components"""
        try:
            return self._calculate_coherence_impl(folded, phase_component)
        except Exception as e:
            logger.warning(f"Coherence calculation failed: {e}")
            return 0.5

    def unfold(self, folded_state: FoldingState) -> np.ndarray:
        """Attempt to unfold data back to the original space.

        Parameters
        ----------
        folded_state : FoldingState
            State to unfold.

        Returns
        -------
        np.ndarray
            Unfolded data approximation.
        """
        try:
            # Reverse unitary evolution applied during folding using cached matrix
            unfolded = folded_state.folded_data @ self._unitary_inv

            return unfolded

        except Exception as e:
            logger.error(f"Error in unfolding operation: {e}")
            return folded_state.folded_data

    async def _compute_fold_error(
        self, original: np.ndarray, timestamp: float
    ) -> float:
        """Return normalized reconstruction error for a fold/unfold cycle.

        Parameters
        ----------
        original
            Data to fold and then unfold.
        timestamp
            Timestamp passed to :meth:`fold`.

        Returns
        -------
        float
            Normalized reconstruction error in ``[0, 1]``.
        """
        folded_state = await self.fold(original, timestamp)
        reconstructed = self.unfold(folded_state)
        denom = np.linalg.norm(original) + 1e-8
        error = np.linalg.norm(reconstructed - original) / denom
        return float(min(max(error, 0.0), 1.0))

    async def evaluate_reversibility(
        self, original: np.ndarray, timestamp: float
    ) -> float:
        """Return score measuring fidelity of fold/unfold cycle.

        Parameters
        ----------
        original
            Original array to fold and unfold.
        timestamp
            Timestamp used for the folding step.

        Returns
        -------
        float
            Reversibility score in ``[0, 1]`` where ``1`` means perfect
            reconstruction.
        """

        error = await self._compute_fold_error(original, timestamp)
        return float(max(0.0, 1.0 - error))

    async def evaluate_information_loss(
        self, original: np.ndarray, timestamp: float
    ) -> float:
        """Return normalized information loss of a fold/unfold cycle.

        Parameters
        ----------
        original
            Input array to transform.
        timestamp
            Timestamp for the folding step.

        Returns
        -------
        float
            Reconstruction error in ``[0, 1]`` where ``0`` means perfect
            reconstruction.

        Examples
        --------
        >>> op = FoldingOperator({})
        >>> arr = np.eye(op.dimensions)
        >>> asyncio.run(op.evaluate_information_loss(arr, 0.0))
        0.0
        """

        error = await self._compute_fold_error(original, timestamp)
        return float(min(max(error, 0.0), 1.0))

    def get_folding_signature(self) -> np.ndarray:
        """Get unique signature of current folding state"""
        if self.current_state is None:
            return np.zeros(self.dimensions)

        # Create signature from folded data statistics
        data = self.current_state.folded_data
        signature = np.array(
            [
                np.mean(data, axis=0),
                np.std(data, axis=0),
                [self.current_state.phase] * self.dimensions,
                [self.current_state.coherence] * self.dimensions,
            ]
        ).flatten()[: self.dimensions]

        return signature

    def is_coherent(self) -> bool:
        """Check if current state meets coherence threshold"""
        if self.current_state is None:
            return False

        return self.current_state.coherence >= self.coherence_threshold

    def calculate_entropy(self) -> float:
        """Return normalized Shannon entropy of the folded state."""

        if self.current_state is None:
            return 0.0

        data = np.abs(self.current_state.folded_data).flatten()
        if data.size == 0:
            return 0.0

        hist, _ = np.histogram(data, bins=min(20, data.size))
        if hist.sum() == 0:
            return 0.0
        probs = hist / hist.sum()
        probs = probs[probs > 0]
        ent = float(np.sum(-probs * np.log(probs)))
        max_ent = np.log(probs.size)
        return ent / max_ent if max_ent > 0 else 0.0

    def get_state_dict(self) -> Dict[str, Any]:
        """Get serializable state dictionary"""
        return {
            "dimensions": self.dimensions,
            "coherence_threshold": self.coherence_threshold,
            "phase_coupling": self.phase_coupling,
            "current_state": (
                {
                    "dimensions": (
                        self.current_state.dimensions if self.current_state else None
                    ),
                    "phase": (
                        float(self.current_state.phase) if self.current_state else None
                    ),
                    "coherence": (
                        float(self.current_state.coherence)
                        if self.current_state
                        else None
                    ),
                    "timestamp": (
                        self.current_state.timestamp if self.current_state else None
                    ),
                }
                if self.current_state
                else None
            ),
            "history_length": len(self.folding_history),
        }

    def history_size(self) -> int:
        """Return the number of stored folding states."""
        return len(self.folding_history)


def apply_folding(
    field: np.ndarray,
    alpha: float | None = 0.1,
    hamiltonian: np.ndarray | None = None,
) -> np.ndarray:
    """Lightweight folding transformation used in tests.

    Parameters
    ----------
    field
        Array representing the quantum field to be folded.
    alpha
        Folding coefficient. If ``None`` the environment variable
        ``QUALIA_FOLDING_ALPHA`` is used.
    hamiltonian
        Optional Hamiltonian matrix to apply a unitary evolution.

    Returns
    -------
    np.ndarray
        Transformed field.
    """

    if not isinstance(field, np.ndarray):
        raise TypeError("field must be a numpy array")

    if alpha is None:
        alpha = float(os.getenv("QUALIA_FOLDING_ALPHA", "0.1"))
    elif not isinstance(alpha, (int, float)):
        raise TypeError("alpha must be a float")
    elif alpha < 0:
        raise ValueError("alpha must be non-negative")

    field = field.astype(float, copy=False)

    if hamiltonian is None:
        return np.exp(-alpha) * field

    if not isinstance(hamiltonian, np.ndarray):
        raise TypeError("hamiltonian must be a numpy array")

    from scipy.linalg import expm

    u = expm(-alpha * hamiltonian)
    return u @ field @ u.T
