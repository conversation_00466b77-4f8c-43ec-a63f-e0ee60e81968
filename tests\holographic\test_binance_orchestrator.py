import asyncio
from types import SimpleNamespace

from qualia.orchestration import BinanceHolographicTradingOrchestrator


class DummyCollector:
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    async def collect_market_data(self):
        return []

    async def collect_news_events(self):
        return []

    def convert_to_holographic_events(self, market, news, field_size):
        return []

    def stop_collection(self):
        pass


class DummyUniverse:
    field_size = (10, 10)

    def __init__(self, *a, **k):
        pass

    async def inject_holographic_event(self, event):
        pass

    async def step_evolution(self, current_time):
        pass

    def analyze_holographic_patterns(self):
        return []

    def generate_trading_signals(self, patterns):
        return []

    def get_field_summary(self):
        return {"total_energy": 0.0}

    async def shutdown(self):
        pass


def test_orchestrator_loads_config(monkeypatch):
    monkeypatch.setattr(
        "qualia.orchestration.binance_orchestrator.RealDataCollector", DummyCollector
    )
    monkeypatch.setattr(
        "qualia.orchestration.binance_orchestrator.HolographicMarketUniverse",
        DummyUniverse,
    )
    monkeypatch.setattr(
        "qualia.orchestration.binance_orchestrator.AdvancedRiskManager",
        lambda *a, **k: SimpleNamespace(),
    )
    monkeypatch.setattr(
        "qualia.orchestration.binance_orchestrator.BinanceClient",
        lambda *a, **k: SimpleNamespace(
            initialize=lambda: asyncio.sleep(0), shutdown=lambda: asyncio.sleep(0)
        ),
    )
    monkeypatch.setattr(
        "qualia.orchestration.binance_orchestrator.HolographicTradingBridge",
        lambda *a, **k: SimpleNamespace(
            process_holographic_signal=lambda *a, **k: asyncio.sleep(0),
            monitor_active_positions=lambda: asyncio.sleep(0),
            get_performance_summary=lambda: {},
        ),
    )

    orch = BinanceHolographicTradingOrchestrator(
        "config/holographic_trading_binance.yaml"
    )

    assert "binance" in orch.config["exchanges"]
    asyncio.run(orch.initialize())
    assert orch.data_collector is not None
    assert orch.holographic_universe is not None
