# Documentação do QUALIA_QAST

Este diretório concentra a documentação em Markdown referente ao projeto. Os arquivos estão organizados em subpastas temáticas:

- **api** – guias de integração e referências das principais APIs do pacote.
- **analysis** – relatórios e auditorias técnicas de experimentos realizados.
- **specs** – documentos conceituais que fundamentam os operadores e a arquitetura.

Arquivos na raiz abordam tópicos gerais, como visão da arquitetura, gerenciamento de risco e evolução futura do sistema.

Para começar a explorar o projeto, consulte:

- [Compreendendo_QUALIA.md](Compreendendo_QUALIA.md)
- [ARCHITECTURE_OVERVIEW.md](ARCHITECTURE_OVERVIEW.md)
- A seção **Operadores quânticos e métricas derivadas** desse arquivo explica
  como `coherence_C`, `entanglement_E`, liquidez, tendência e Δ-entropia são
  calculados e utilizados na HUD.

Outros documentos úteis:

- [Conhecendo_Core.md](Conhecendo_Core.md)
- [Conhecendo_Estrategias.md](Conhecendo_Estrategias.md)
- [no_op_strategy.md](no_op_strategy.md)
- [Conhecendo_Market.md](Conhecendo_Market.md)
- [dev_interface.md](dev_interface.md)
- [Conhecendo_Memoria.md](Conhecendo_Memoria.md)
- [Conhecendo_Metacognicao.md](Conhecendo_Metacognicao.md)
- [EXPLORACOES_CONCEITUAIS.md](EXPLORACOES_CONCEITUAIS.md)
- [ISSUE_TRACKER.md](ISSUE_TRACKER.md)
- [PLACEHOLDER_MODULES.md](PLACEHOLDER_MODULES.md)
- [TODO_STATUS.md](TODO_STATUS.md)
- [UNIVERSE_IMPROVEMENTS.md](../src/qualia/core/UNIVERSE_IMPROVEMENTS.md)
- [logging.md](logging.md)
- [risk_management.md](risk_management.md)
- [risk_management_builder.md](risk_management_builder.md)
- [roadmap.md](roadmap.md)
- [holographic_metrics_mapping.md](holographic_metrics_mapping.md)

Os módulos `folding.py`, `resonance.py`, `emergence.py`,
`retrocausality.py` e `observer.py` em `src/qualia/core` possuem
implementações parciais e experimentais. O arquivo `AGENTS.md` (linhas
8-15) resume o estado atual desses operadores. Consulte também
[PLACEHOLDER_MODULES.md](PLACEHOLDER_MODULES.md) para detalhes sobre as
limitações e planos de evolução.


## Subpastas

### analysis
- [analysis/stability_analysis_report.md](analysis/stability_analysis_report.md)
- [analysis/strategies_audit.md](analysis/strategies_audit.md)
- [quantum_operator_prototype.md](quantum_operator_prototype.md)

### api
- [api/encoder_timeframe_alignment.md](api/encoder_timeframe_alignment.md)
- [api/encoding_features.md](api/encoding_features.md)
- [api/json_persistence.md](api/json_persistence.md)
- [api/kraken_integration.md](api/kraken_integration.md)
- [api/kraken_ticker_strategy.md](api/kraken_ticker_strategy.md)
- [api/kucoin_integration.md](api/kucoin_integration.md)
- [api/binance_integration.md](api/binance_integration.md)
- [api/memory.md](api/memory.md)
- [api/metacognition.md](api/metacognition.md)
- [api/metrics.md](api/metrics.md)
- [api/qast_evolution_metrics.md](api/qast_evolution_metrics.md)
- [api/qast_core.md](api/qast_core.md)
- [api/risk_metacognition.md](api/risk_metacognition.md)
- [api/signal_generation_flow.md](api/signal_generation_flow.md)
- [api/sim_graph.md](api/sim_graph.md) <!-- novo -->
- [api/system_manager.md](api/system_manager.md) <!-- documentação do gerenciador -->
- [api/state_management.md](api/state_management.md)
- [api/temporal_detector.md](api/temporal_detector.md)
- [api/temporal_pattern_config.md](api/temporal_pattern_config.md)
- [api/trading_engine.md](api/trading_engine.md)
- [api/README.md](api/README.md)

### specs
- [specs/CTX-CortexPlusPlus.md](specs/CTX-CortexPlusPlus.md)
- [specs/Modelo Integrado da Consciência Quântica e da Informação (MICQI).md](specs/Modelo Integrado da Consciência Quântica e da Informação (MICQI).md)
- [specs/QUALIA_Trading_System_VFinal_PT-BR.md](specs/QUALIA_Trading_System_VFinal_PT-BR.md)
- [specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md](specs/QUALIA_Trading_System_VFinal_PT-BR_EXT.md)
- [specs/SEN-EncodersV2.md](specs/SEN-EncodersV2.md)
- [specs/quantum_operator_overview.md](specs/quantum_operator_overview.md)

## Executando Testes

Para rodar a suíte de testes localmente utilize o script `scripts/install_test_deps.sh`,
que instala as dependências principais e as de `requirements-test.txt`. Em seguida
execute `pytest tests/` normalmente.

Scripts de exploração manual ficam no diretório `examples/` e
não fazem parte da suíte automática de testes.
Para exemplos de depuracao, consulte [debug_scripts.md](debug_scripts.md).

## Utilizando `collapse_by_coherence`

A função `collapse_by_coherence` mede a coerência entre dois vetores de intenção
(um proveniente do mercado e outro da simulação social). Ela retorna a ação
recomendada, o grau de confiança e indica paradoxos quando há alinhamento
negativo.

```python
from qualia.utils.coherence_collapse import collapse_by_coherence

decisao = collapse_by_coherence([1, 0, 0], [0.8, 0.1, 0.1])
print(decisao["action"], decisao["coherence"])
```

O `QASTOracleDecisionEngine` utiliza esse resultado para unificar sinais
internos e externos antes de gerar a decisão final de trading.

