"""
Metacognition System Module

Provides metacognitive capabilities for self-reflection, strategy adaptation,
and performance optimization.
"""

from .system import MetacognitionSystem

# MetacognitiveContext may be missing when running with minimal stubs
try:  # pragma: no cover - optional during unit tests
    from .metacognition_trading import (
        QUALIAMetacognitionTrading,
        MetacognitiveContext,
    )
except Exception:  # pragma: no cover
    from .metacognition_trading import QUALIAMetacognitionTrading

    class MetacognitiveContext:  # type: ignore[misc]
        """Fallback context used when stubs omit the real implementation."""

        pass


from .coherence_guard import CoherenceGuard
from .layer import QuantumMetacognitionLayer, MetacognitionConfig
from .analyzer import MetacogClusterAnalyzer, ClusterInsights
from .service import get_focus, push_metric, snapshot, get_insights

__all__ = [
    "QuantumMetacognitionLayer",
    "MetacognitionConfig",
    "QUALIAMetacognitionTrading",
    "MetacognitiveContext",
    "CoherenceGuard",
    "MetacogClusterAnalyzer",
    "ClusterInsights",
    "get_insights",
    "get_focus",
    "push_metric",
    "snapshot",
]
