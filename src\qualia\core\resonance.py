"""Resonance Operator - Harmonic frequency analysis and synchronization.

Implements quantum-inspired resonance detection for market cycles.
"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
from scipy import signal

from typing import TYPE_CHECKING

from ..common_types import QuantumSignaturePacket

if TYPE_CHECKING:  # pragma: no cover - optional heavy deps
    from qualia.memory.system import MemorySystem
    from qualia.memory.quantum_pattern_memory import QuantumPatternMemory
from ..utils.cache import numpy_cache
from ..utils.numba_utils import optional_njit

logger = logging.getLogger(__name__)


@dataclass
class ResonanceState:
    """State representation for resonance operations"""

    frequencies: np.ndarray
    amplitudes: np.ndarray
    phases: np.ndarray
    harmonics: List[float]
    resonance_strength: float
    timestamp: float


class ResonanceOperator:
    """
    Quantum-inspired resonance operator for harmonic analysis

    Detects and analyzes harmonic frequencies in market data,
    identifying resonant patterns and synchronization events.
    """

    def __init__(self, config: Dict[str, Any], *, history_maxlen: int = 1000):
        self.harmonic_frequencies = config.get(
            "harmonic_frequencies", [1, 2, 3, 5, 8, 13, 21]
        )
        self.amplitude_threshold = config.get("amplitude_threshold", 0.1)
        self.phase_lock_tolerance = config.get("phase_lock_tolerance", 0.05)

        # Convert to numpy array and normalize
        self.harmonic_frequencies = np.array(self.harmonic_frequencies, dtype=float)
        self.base_frequency = self.harmonic_frequencies[0]

        # Initialize resonance tracking
        self.current_state: Optional[ResonanceState] = None
        self.resonance_history: List[ResonanceState] = []
        self.history_maxlen = int(history_maxlen)
        self.phase_locked_pairs: List[Tuple[int, int]] = []

        logger.info(
            f"ResonanceOperator initialized with {len(self.harmonic_frequencies)} harmonics"
        )

    async def analyze_resonance(
        self,
        market_data: np.ndarray,
        timestamp: float,
        sampling_rate: float = 1.0,
    ) -> ResonanceState:
        """Analyze resonance patterns in market data.

        Parameters
        ----------
        market_data : np.ndarray
            Input market data time series.
        timestamp : float
            Current timestamp.
        sampling_rate : float, optional
            Data sampling rate in hertz.

        Returns
        -------
        ResonanceState
            Resulting harmonic analysis.
        """
        try:
            # Ensure data is 1D
            if len(market_data.shape) > 1:
                market_data = market_data.flatten()

            # Minimum data length check - BOOTSTRAP FIX: Reduce log level during bootstrap
            if len(market_data) < 32:
                # Check if we're in bootstrap phase to reduce log noise
                log_level = (
                    "info"
                    if hasattr(self, "_bootstrap_phase") and self._bootstrap_phase
                    else "warning"
                )
                if log_level == "info":
                    logger.info(
                        "Insufficient data for resonance analysis (bootstrap phase)"
                    )
                else:
                    logger.warning("Insufficient data for resonance analysis")
                return self._create_empty_state(timestamp)

            # Compute Fourier transform
            fft_data = np.fft.fft(market_data)
            frequencies = np.fft.fftfreq(len(market_data), 1 / sampling_rate)

            # Extract positive frequencies
            positive_freq_mask = frequencies > 0
            pos_frequencies = frequencies[positive_freq_mask]
            pos_amplitudes = np.abs(fft_data[positive_freq_mask])
            pos_phases = np.angle(fft_data[positive_freq_mask])

            # Find harmonic components
            harmonic_amplitudes = []
            harmonic_phases = []
            detected_harmonics = []

            for harmonic_freq in self.harmonic_frequencies:
                # Scale harmonic frequency by base frequency
                target_freq = harmonic_freq * self.base_frequency / sampling_rate

                # Find closest frequency bin
                if len(pos_frequencies) > 0:
                    freq_idx = np.argmin(np.abs(pos_frequencies - target_freq))

                    if freq_idx < len(pos_amplitudes):
                        amplitude = pos_amplitudes[freq_idx]
                        phase = pos_phases[freq_idx]

                        # Check if amplitude meets threshold
                        if amplitude >= self.amplitude_threshold:
                            harmonic_amplitudes.append(amplitude)
                            harmonic_phases.append(phase)
                            detected_harmonics.append(harmonic_freq)
                        else:
                            harmonic_amplitudes.append(0.0)
                            harmonic_phases.append(0.0)
                    else:
                        harmonic_amplitudes.append(0.0)
                        harmonic_phases.append(0.0)
                else:
                    harmonic_amplitudes.append(0.0)
                    harmonic_phases.append(0.0)

            # Calculate resonance strength
            resonance_strength = self._calculate_resonance_strength(
                np.array(harmonic_amplitudes), np.array(harmonic_phases)
            )

            # Detect phase locking
            self.phase_locked_pairs = self._detect_phase_locking(
                np.array(harmonic_phases), self.phase_lock_tolerance
            )

            # Create resonance state
            state = ResonanceState(
                frequencies=pos_frequencies[
                    : min(len(pos_frequencies), 100)
                ],  # Limit size
                amplitudes=np.array(harmonic_amplitudes),
                phases=np.array(harmonic_phases),
                harmonics=detected_harmonics,
                resonance_strength=resonance_strength,
                timestamp=timestamp,
            )

            self.current_state = state
            self.resonance_history.append(state)

            # Limit history size
            if len(self.resonance_history) > self.history_maxlen:
                self.resonance_history.pop(0)

            logger.debug(
                f"Resonance analysis complete: strength={resonance_strength:.3f}, "
                f"harmonics={len(detected_harmonics)}"
            )

            return state

        except Exception as e:
            logger.error(f"Error in resonance analysis: {e}")
            return self._create_empty_state(timestamp)

    @staticmethod
    @optional_njit(cache=True)
    def _calculate_resonance_strength_impl(
        amplitudes: np.ndarray, phases: np.ndarray
    ) -> float:
        if len(amplitudes) == 0:
            return 0.0

        amplitude_strength = np.mean(amplitudes) / (np.max(amplitudes) + 1e-8)
        phase_coherence = ResonanceOperator._calculate_phase_coherence(phases)
        harmonic_strength = ResonanceOperator._calculate_harmonic_strength(amplitudes)
        resonance_strength = (
            amplitude_strength * 0.4 + phase_coherence * 0.4 + harmonic_strength * 0.2
        )
        return np.clip(resonance_strength, 0.0, 1.0)

    def _calculate_resonance_strength(
        self, amplitudes: np.ndarray, phases: np.ndarray
    ) -> float:
        """Calculate overall resonance strength from harmonic components"""
        try:
            return self._calculate_resonance_strength_impl(amplitudes, phases)
        except Exception as e:
            logger.warning(f"Resonance strength calculation failed: {e}")
            return 0.0

    @staticmethod
    @optional_njit(cache=True)
    def _calculate_phase_coherence(phases: np.ndarray) -> float:
        """Calculate phase coherence measure"""
        if len(phases) < 2:
            return 0.0

        # Calculate phase differences
        phase_diffs = np.diff(phases)

        # Wrap phase differences to [-π, π]
        phase_diffs = np.angle(np.exp(1j * phase_diffs))

        # Calculate coherence as inverse of phase variance
        phase_variance = np.var(phase_diffs)
        coherence = np.exp(-phase_variance)

        return coherence

    @staticmethod
    @optional_njit(cache=True)
    def _calculate_harmonic_strength(amplitudes: np.ndarray) -> float:
        """Calculate strength based on harmonic relationships"""
        if len(amplitudes) < 2:
            return 0.0

        golden_ratio = 1.618033988749

        valid = amplitudes > 0
        amps = amplitudes[valid]
        if len(amps) < 2:
            return 0.0

        ratios = amps[np.newaxis, :] / amps[:, np.newaxis]
        upper = np.triu_indices(len(amps), k=1)
        ratios = ratios[upper]

        strength = (
            np.sum(np.abs(ratios - golden_ratio) < 0.1)
            + np.sum(np.abs(ratios - 1 / golden_ratio) < 0.1)
            + 0.8
            * (np.sum(np.abs(ratios - 2.0) < 0.1) + np.sum(np.abs(ratios - 0.5) < 0.1))
        )

        count = len(ratios)
        return strength / max(count, 1)

    @staticmethod
    @optional_njit(cache=True)
    def _detect_phase_locking(
        phases: np.ndarray, tolerance: float
    ) -> list[tuple[int, int]]:
        """Return list of phase-locked harmonic index pairs"""
        locked = []
        if len(phases) < 2:
            return locked

        phase_array = np.asarray(phases, dtype=float)
        diffs = np.abs(phase_array[:, None] - phase_array[None, :])
        diffs = np.minimum(diffs, 2 * np.pi - diffs)
        mask = np.triu(np.ones_like(diffs, dtype=bool), k=1)
        idxs = np.argwhere((diffs < tolerance) & mask)
        return [(int(i), int(j)) for i, j in idxs]

    def _create_empty_state(self, timestamp: float) -> ResonanceState:
        """Create empty resonance state for error cases"""
        return ResonanceState(
            frequencies=np.array([]),
            amplitudes=np.zeros(len(self.harmonic_frequencies)),
            phases=np.zeros(len(self.harmonic_frequencies)),
            harmonics=[],
            resonance_strength=0.0,
            timestamp=timestamp,
        )

    def is_resonant(self, threshold: float = 0.5) -> bool:
        """Check if current state shows strong resonance"""
        if self.current_state is None:
            return False

        return self.current_state.resonance_strength >= threshold

    def get_dominant_harmonic(self) -> Optional[float]:
        """Get the dominant harmonic frequency"""
        if self.current_state is None or len(self.current_state.harmonics) == 0:
            return None

        # Find harmonic with highest amplitude
        max_idx = np.argmax(self.current_state.amplitudes)

        if max_idx < len(self.current_state.harmonics):
            return self.current_state.harmonics[max_idx]

        return None

    def get_resonance_pattern(self) -> Dict[str, Any]:
        """Get current resonance pattern summary"""
        if self.current_state is None:
            return {}

        return {
            "strength": float(self.current_state.resonance_strength),
            "dominant_harmonic": self.get_dominant_harmonic(),
            "active_harmonics": len(self.current_state.harmonics),
            "phase_locked_pairs": len(self.phase_locked_pairs),
            "timestamp": self.current_state.timestamp,
        }

    def calculate_phase_alignment(self) -> float:
        """Return overall phase alignment among detected harmonics."""
        if self.current_state is None or len(self.current_state.phases) < 2:
            return 0.0
        diffs = np.diff(self.current_state.phases)
        diffs = np.angle(np.exp(1j * diffs))
        return float(1.0 - np.var(diffs) / np.pi)

    def calculate_entropy(self) -> float:
        """Return normalized entropy of the harmonic amplitudes."""

        if self.current_state is None or self.current_state.amplitudes.size == 0:
            return 0.0

        amps = np.abs(self.current_state.amplitudes)
        probs = amps / (np.sum(amps) + 1e-12)
        probs = probs[probs > 0]
        ent = float(np.sum(-probs * np.log(probs)))
        max_ent = np.log(probs.size)
        return ent / max_ent if max_ent > 0 else 0.0

    def phase_lock_ratio(self) -> float:
        """Return ratio of phase-locked harmonic pairs to all possible pairs.

        Returns
        -------
        float
            Ratio of locked harmonic pairs in ``[0, 1]``.

        Examples
        --------
        >>> op = ResonanceOperator({})
        >>> op.current_state = ResonanceState(
        ...     frequencies=np.array([]),
        ...     amplitudes=np.array([]),
        ...     phases=np.array([]),
        ...     harmonics=[],
        ...     resonance_strength=0.0,
        ...     timestamp=0.0,
        ... )
        >>> op.phase_locked_pairs = []
        >>> op.phase_lock_ratio()
        0.0
        """

        if self.current_state is None or len(self.current_state.phases) < 2:
            return 0.0

        n = len(self.current_state.phases)
        total_pairs = n * (n - 1) / 2
        locked = len(self.phase_locked_pairs)
        return float(locked / total_pairs) if total_pairs else 0.0

    def get_state_dict(self) -> Dict[str, Any]:
        """Get serializable state dictionary"""
        return {
            "harmonic_frequencies": self.harmonic_frequencies.tolist(),
            "amplitude_threshold": self.amplitude_threshold,
            "phase_lock_tolerance": self.phase_lock_tolerance,
            "current_resonance_strength": (
                float(self.current_state.resonance_strength)
                if self.current_state
                else 0.0
            ),
            "phase_locked_pairs": len(self.phase_locked_pairs),
            "history_length": len(self.resonance_history),
        }

    def history_size(self) -> int:
        """Return the number of stored resonance states."""
        return len(self.resonance_history)

    def retrieve_similar_patterns(
        self, memory_system: "MemorySystem", top_n: int = 5
    ) -> List[Dict[str, Any]]:
        """Query :class:`MemorySystem` for patterns similar to the current state."""

        if self.current_state is None:
            return []

        return memory_system.query_resonance_patterns(self.current_state, top_n=top_n)


PHI = (1 + np.sqrt(5)) / 2


def _fetch_historical_patterns_impl(
    field: np.ndarray,
    memory: "QuantumPatternMemory",
    top_n: int = 5,
) -> Tuple[List[np.ndarray], np.ndarray]:
    """Return patterns and resonance coefficients from ``memory``.

    Parameters
    ----------
    field
        Reference vector used to query the memory.
    memory
        Instance of :class:`QuantumPatternMemory`.
    top_n
        Maximum number of patterns to retrieve.

    Returns
    -------
    list of ndarray
        Pattern vectors retrieved.
    ndarray
        Coefficients :math:`\lambda_i` associated with each pattern based on
        similarity scores.
    """

    results = memory.retrieve_historical_patterns(field, top_n=top_n)
    patterns: List[np.ndarray] = []
    coeffs: List[float] = []

    for item in results:
        pattern_id = item.get("metadata", {}).get("pattern_id")
        if not pattern_id:
            continue
        stored = memory.retrieve(pattern_id)
        if not stored:
            continue
        qsp = stored.get("qsp_obj")
        if qsp is None:
            qsp_dict = stored.get("quantum_signature_packet")
            if isinstance(qsp_dict, dict):
                try:
                    qsp = QuantumSignaturePacket(**qsp_dict)
                except Exception:
                    continue
        if qsp is None:
            continue
        vec = np.asarray(qsp.vector, dtype=float)
        if vec.shape != field.shape:
            vec = np.resize(vec, field.shape)
        patterns.append(vec)
        coeffs.append(float(item.get("similarity", 0.0)))

    return patterns, np.asarray(coeffs, dtype=float)


@numpy_cache(maxsize=32)
def _cached_historical_patterns(
    field: np.ndarray,
    memory: "QuantumPatternMemory",
    top_n: int = 5,
) -> Tuple[List[np.ndarray], np.ndarray]:
    """Cached wrapper for :func:`_fetch_historical_patterns_impl`."""

    return _fetch_historical_patterns_impl(field, memory, top_n=top_n)


def invalidate_pattern_cache() -> None:
    """Clear cached historical pattern entries."""

    _cached_historical_patterns.cache_clear()


def _fetch_historical_patterns(
    field: np.ndarray,
    memory: "QuantumPatternMemory",
    top_n: int = 5,
    *,
    use_cache: bool = True,
) -> Tuple[List[np.ndarray], np.ndarray]:
    """Return patterns and resonance coefficients with optional caching."""

    if use_cache:
        return _cached_historical_patterns(field, memory, top_n=top_n)
    return _fetch_historical_patterns_impl(field, memory, top_n=top_n)


def identify_dominant_resonances(
    field: np.ndarray,
    memory: "QuantumPatternMemory",
    *,
    top_n: int = 5,
    sampling_rate: float = 1.0,
    use_cache: bool = True,
) -> Dict[str, Any]:
    """Identify dominant frequencies using historical patterns.

    Parameters
    ----------
    field
        Current field used as reference.
    memory
        Instance of :class:`QuantumPatternMemory`.
    top_n
        Number of historical patterns to combine.
    sampling_rate
        Sampling rate applied when computing the periodogram.
    use_cache
        When ``True``, reuse cached patterns for repeated calls.
    """

    patterns, coeffs = _fetch_historical_patterns(
        field, memory, top_n=top_n, use_cache=use_cache
    )
    combined = apply_resonance(
        field,
        strength=None,
        patterns=patterns,
        coefficients=coeffs,
    )

    freqs, power = signal.periodogram(combined, fs=sampling_rate)
    idx = int(np.argmax(power)) if power.size > 0 else 0
    dominant = float(freqs[idx]) if freqs.size > 0 else 0.0

    return {
        "frequencies": freqs,
        "power": power,
        "dominant_frequency": dominant,
    }


def apply_resonance(
    field: np.ndarray,
    strength: float | None = 0.1,
    phi: float = PHI,
    patterns: Optional[List[np.ndarray]] = None,
    coefficients: Optional[List[float]] = None,
    *,
    memory: Optional["QuantumPatternMemory"] = None,
    top_n: int = 5,
    use_cache: bool = True,
) -> np.ndarray:
    """Basic resonance transformation used in unit tests.

    Parameters
    ----------
    field
        Input field to transform.
    strength
        Resonance strength coefficient. ``None`` uses the configured default.
    phi
        Phase constant.
    patterns
        Optional list of additional patterns to superimpose.
    coefficients
        Coefficients for the provided patterns.
    memory
        Optional :class:`QuantumPatternMemory` for historical pattern retrieval.
    top_n
        Number of historical patterns to consider when ``memory`` is provided.
    use_cache
        When ``True``, retrieved patterns are cached for faster repeated calls.
    """

    if not isinstance(field, np.ndarray):
        raise TypeError("field must be a numpy array")

    if strength is None:
        from ..config import config as config_pkg

        strength = config_pkg.resonance_strength
    elif not isinstance(strength, (int, float)):
        raise TypeError("strength must be a float")

    result = field + strength * np.sin(phi * field)

    if memory is not None and patterns is None and coefficients is None:
        patterns, coefficients = _fetch_historical_patterns(
            field, memory, top_n=top_n, use_cache=use_cache
        )

    if patterns is not None and coefficients is not None:
        for pat, coeff in zip(patterns, coefficients):
            result = result + coeff * pat
    return result
