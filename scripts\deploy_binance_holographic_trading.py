#!/usr/bin/env python3
"""Deployment do BinanceHolographicTradingOrchestrator."""

import asyncio
import logging
import os
import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent / "src"))

try:
    from dotenv import load_dotenv

    load_dotenv()
    print("✅ Arquivo .env carregado")
except Exception:
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                if line.strip() and not line.startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value
        print("✅ Arquivo .env carregado manualmente")
    else:
        print("⚠️ Arquivo .env não encontrado")

from qualia.orchestration import BinanceHolographicTradingOrchestrator
from qualia.utils.logger import get_logger

logger = get_logger(__name__)


async def main() -> None:
    """Inicializa e executa o orchestrator."""
    # Resolve caminho absoluto para o arquivo de configuração
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    config_path = project_root / "config" / "holographic_trading_binance.yaml"
    
    if not config_path.exists():
        logger.error(f"❌ Arquivo de configuração não encontrado: {config_path}")
        return
    
    orchestrator = BinanceHolographicTradingOrchestrator(str(config_path))
    await orchestrator.initialize()
    try:
        await orchestrator.run_trading_loop()
    except KeyboardInterrupt:
        logger.info("⏹️ Interrupção pelo usuário")
    finally:
        await orchestrator.shutdown()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
