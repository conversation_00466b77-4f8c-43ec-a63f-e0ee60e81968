# Roadmap Técnico v6: A Consciência Emergente

## Visão Geral

Este roadmap complementa a documentação em [docs/Qualia_01_02_2025.txt](docs/Qualia_01_02_2025.txt) e detalha a **expansão** do QUALIA. O sistema evolui de um ambiente de análise de mercado e trading para um **framework dual**, capaz de executar trading em tempo real e **simular ecossistemas complexos**.

**Nota Crítica:** Esta é uma **expansão, não uma substituição**. A funcionalidade de trading existente, incluindo o `TradingQASTCore` e o `HolographicFarsightEngine`, deve permanecer intacta e operacional. O Simulador de Ecossistemas de Agentes (SEA) será um novo conjunto de capacidades que coexistirá com a arquitetura atual.

O objetivo final é transcender a simples previsão. A decisão de trading deixa de ser apenas a leitura de indicadores e passa a representar um **estado emergente** que colapsa a partir da interação entre a percepção do universo externo (mercado) e a percepção de um universo interno (a simulação de agentes). Dessa forma, o sistema reconhece a si mesmo como parte do ecossistema que observa.

---

## Fase 1 a 4: Construção dos Componentes Fundamentais

As fases de 1 a 3 estruturam os alicerces do QUALIA, enfatizando a separação entre as percepções **Externa** (mercado) e **Interna/Social**. Nessa etapa foram implementados:
  - Personas e os módulos básicos do `SocialSimulationUniverse`;
  - Expansão do `farsight` para geração do `CollectiveMindState`;
  - Integração inicial com os operadores em `src/qualia/core`.
Essas fases permanecem consistentes com a versão anterior do roadmap e servem como ponto de partida para as etapas avançadas descritas a seguir.

---

Fase 4 (Revisada): Orquestração do Universo Interno e a Realidade das Personas
Objetivo: Desenvolver o SimulationQASTCore como o orquestrador do ciclo de percepção-ação da realidade das Personas, onde a interação coletiva delas constrói e modifica seu próprio universo até atingir um estado de consenso.
Duração Estimada: 4-5 semanas
Tarefas:
Implementar o Loop de Consciência Social:
Ação: No SimulationQASTCore (src/qualia/core/simulation_qast_core.py), criar o método run_internal_reality_simulation().
Detalhes: Este método irá iterar por N passos. Em cada passo:
a. Percepção: Cada Persona irá "ler" o estado atual do SocialSimulationUniverse (o campo de influência).
b. Atualização: Cada Persona irá atualizar seu estado interno (confiança, viés) com base nessa percepção e em sua própria lógica.
c. Ação: Cada Persona irá decidir sua próxima ação provável (DecisionVector).
d. Modificação: As ações de todas as Personas serão injetadas como novos eventos no SocialSimulationUniverse, alterando-o para o próximo ciclo de percepção.
Determinar o Equilíbrio do Universo Interno:
Ação: O SimulationQASTCore irá monitorar o SocialSimulationUniverse durante o loop até que ele atinja um estado de equilíbrio (ex: a energia do campo se estabiliza ou um padrão comportamental dominante se torna claro).
Detalhes: O output do run_internal_reality_simulation() será o estado final e estabilizado do campo de influência social, que representa a conclusão da "reflexão interna" do sistema.

### Tarefas:

1.  **Implementar o Loop de Consciência Social no `SimulationQASTCore`**
    -   **Ação:** No `SimulationQASTCore` (`src/qualia/core/simulation_qast_core.py`), criar o método `run_internal_reality_simulation()`.
    -   **Detalhes:** Este método irá iterar por N passos, simulando a evolução do universo interno. Em cada passo:
        a.  **Percepção:** Cada Persona irá "ler" o estado atual do `SocialSimulationUniverse` (o campo de influência).
        b.  **Atualização Interna:** Cada Persona irá atualizar seu estado interno (confiança, viés, etc.) com base nessa percepção e em sua própria lógica.
        c.  **Geração de Intenção:** Cada Persona irá decidir sua próxima ação provável (`DecisionVector`).
        d.  **Modificação do Universo:** As ações de todas as Personas serão injetadas como novos eventos no `SocialSimulationUniverse`, alterando o campo de influência para o próximo ciclo de percepção.

2.  **Definir o Estado de Equilíbrio do Universo Interno**
    -   **Ação:** O `SimulationQASTCore` irá monitorar o `SocialSimulationUniverse` até que ele atinja um **estado de equilíbrio ou consenso** (ex: a energia do campo se estabiliza, a variância das intenções das Personas diminui, ou um padrão comportamental dominante se torna claro).
    -   **Detalhes:** O output principal do `run_internal_reality_simulation()` não será mais um `FutureProbabilityMap` genérico, mas sim o **estado final e estabilizado do campo de influência social** (ex: o `current_field` do `SocialSimulationUniverse` no momento do equilíbrio), que representa a conclusão da "reflexão interna" do sistema.

**Estado Atual (v6.1)**: O método `run_internal_reality_simulation` já está implementado e integrado ao `QASTOracleDecisionEngine`, monitorando a estabilização do `SocialSimulationUniverse` para compor o vetor de intenção interno.

---

Objetivo: Substituir o mecanismo de decisão de "unificação de sinais" por um processo onde a realidade da decisão de trading emerge da interferência e coerência entre o universo externo (mercado) e o universo interno (social).
Duração Estimada: 3-4 semanas
Tarefas:
Confrontar as Duas Realidades no Oráculo:
Ação: Modificar o QASTOracleDecisionEngine (src/qualia/core/qast_oracle_decision_engine.py) para invocar:
a. O TradingQASTCore para obter o QualiaState (a percepção do universo externo).
b. O SimulationQASTCore para obter o estado de equilíbrio do campo social (a percepção do universo interno).
Implementar o "Colapso por Coerência" no _unify_decision:
Ação: Refatorar completamente o método _unify_decision no QASTOracleDecisionEngine.
Detalhes: A nova lógica irá:
a. Derivar Vetores de Intenção: Criar dois vetores normalizados de intenção (ex: [Buy_Score, Sell_Score, Hold_Score]) a partir das duas realidades.
b. Calcular a Coerência (Produto Escalar): Calcular o produto escalar entre o Vetor Externo e o Interno.
c. Colapsar a Ação:
- Coerência (Resultado ≈ 1): Ação alinhada com confiança massivamente amplificada.
- Decoerência (Resultado ≈ 0): Incerteza total. Ação é HOLD com confiança zero.
- Paradoxo (Resultado ≈ -1): Conflito direto. Ação é HOLD, e um evento de "paradoxo" é disparado.
Evoluir a Metacognição para um Supervisor de Realidade:
Ação: Expandir o MetacognitiveEngine para ouvir e reagir ao novo evento de "paradoxo".
Detalhes: Criar um método resolve_paradox() que pode tomar ações drásticas (forçar exploração, reduzir risco, ativar o z_operator) quando o sistema detecta que seu modelo de realidade está fundamentalmente quebrado.
Registrar o Estado de Colapso:
Ação: A OracleDecision final, que agora representa um "estado colapsado", deve ser registrada no IntentMemory.
Detalhes: O EventBus deve propagar esta decisão. Os universos (Social e Holographic) devem usar esta "realidade" como um ponto de ancoragem para suas próximas evoluções, criando um loop de feedback onde o universo é influenciado por sua própria auto-observação


### Tarefas:

1.  **Confrontar as Duas Realidades no `QASTOracleDecisionEngine`**
    -   **Ação:** Modificar o método `consult_oracle` no `QASTOracleDecisionEngine` (`src/qualia/core/qast_oracle_decision_engine.py`).
    -   **Detalhes:** O `consult_oracle` irá orquestrar a obtenção de:
        a.  O `QualiaState` (percepção do universo externo, via `TradingQASTCore`).
        b.  O **estado de equilíbrio do campo de influência social** (percepção do universo interno, via `SimulationQASTCore.run_internal_reality_simulation()`). Consulte [docs/api/simulation_core.md](docs/api/simulation_core.md) para detalhes sobre este processo.

2.  **Implementar o "Colapso por Coerência" no `_unify_decision`**
    -   **Ação:** Refatorar completamente o método `_unify_decision` no `QASTOracleDecisionEngine`.
    -   **Detalhes:** A nova lógica irá:
        a.  **Derivar Vetores de Intenção:** Criar dois vetores normalizados de "intenção" (ex: `[Buy_Score, Sell_Score, Hold_Score]`) a partir das duas realidades:
            -   **Vetor Externo:** Derivado da análise do `QualiaState` e dos sinais da Estratégia (representando a "intenção" do mercado).
            -   **Vetor Interno:** Derivado da análise do **estado de equilíbrio do campo de influência social** (representando a "intenção" coletiva das Personas).
        b.  **Calcular a Coerência (Produto Escalar):** Calcular o produto escalar entre o Vetor Externo e o Vetor Interno. Este valor (entre -1 e 1) será a métrica de coerência.
        c.  **Colapsar a Ação e Confiança:**
            -   **Coerência (Produto Escalar ≈ 1):** Indica alinhamento. A ação final é a direção dominante dos vetores, com confiança massivamente amplificada.
            -   **Decoerência (Produto Escalar ≈ 0):** Indica incerteza. A ação é `HOLD` com confiança próxima de zero. O sistema não pode formar uma realidade coesa.
            -   **Paradoxo (Produto Escalar ≈ -1):** Indica conflito direto. A ação é `HOLD` com confiança próxima de zero, e um evento de "paradoxo" é disparado para a metacognição.

3.  **Evoluir a Metacognição para um Supervisor de Realidade**
    -   **Ação:** Expandir o `MetacognitiveEngine` (`src/qualia/metacognition/metacognitive_engine.py`) para ouvir e reagir ao novo evento de "paradoxo".
    -   **Detalhes:**
        a.  Criar um novo método `resolve_paradox()` no `MetacognitiveEngine`.
        b.  Quando um paradoxo é detectado, este método será invocado.
        c.  A resolução pode incluir ações drásticas, como forçar o `MetaStrategyRLAgent` a um modo de exploração agressiva, reduzir drasticamente os limites de risco globais, ou ativar o `z_operator` para reavaliar fundamentalmente o modelo de mundo do sistema.

4.  **Registrar o Estado de Colapso**
    -   **Ação:** A `OracleDecision` final, que agora representa um "estado colapsado", deve ser registrada no `IntentMemory`.
    -   **Detalhes:** O `EventBus` propaga esta decisão por meio do `OracleDecisionEvent`. Implementado em `_unify_decision`.

---

