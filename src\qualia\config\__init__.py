"""QUALIA configuration entry point with lazy imports."""

from __future__ import annotations

import importlib
from typing import Optional, Any
from types import SimpleNamespace

__all__ = [
    "config",
    "RiskProfileSettings",
    "_FALLBACK_RISK_PROFILE_SETTINGS",
    "get_global_config_loader",
    "get_global_config_manager",
    "register_encoder",
    "unregister_encoder",
    "get_encoder_class",
    "create_encoder",
    "get_registered_encoders",
    "load_quantum_risk_defaults",
    "load_dynamic_risk_defaults",
    "load_signal_arbiter_defaults",
    "load_utils_defaults",
    "load_trading_defaults",
    "load_market_defaults",
    "load_composite_defaults",
    "VectorType",
    "feature_toggle",
    "get_min_counts_diversity_ratio",
    "logging",
]

_MODULE_MAP = {
    "config": (".settings", "settings"),
    "RiskProfileSettings": ".risk_profiles",
    "_FALLBACK_RISK_PROFILE_SETTINGS": ".risk_profiles",
    "VectorType": ".vector_types",
    "ConfigLoader": ".config_loader",
    "ConfigManager": ".config_manager",
    "register_encoder": ".encoder_registry",
    "unregister_encoder": ".encoder_registry",
    "get_encoder_class": ".encoder_registry",
    "create_encoder": ".encoder_registry",
    "get_registered_encoders": ".encoder_registry",
    "load_quantum_risk_defaults": ".quantum_risk_defaults",
    "load_dynamic_risk_defaults": ".dynamic_risk_defaults",
    "load_signal_arbiter_defaults": ".signal_arbiter_defaults",
    "load_utils_defaults": ".utils_defaults",
    "load_trading_defaults": ".trading_defaults",
    "load_market_defaults": ".market_defaults",
    "load_composite_defaults": ".composite_defaults",
    "feature_toggle": ".feature_flags",
    "get_min_counts_diversity_ratio": ".constants",
}

_global_loader: Optional[Any] = None
_global_manager: Optional[Any] = None


def get_global_config_loader() -> ConfigLoader:
    """Return a process-wide :class:`ConfigLoader` instance."""

    global _global_loader
    if _global_loader is None:
        ConfigLoader = __getattr__("ConfigLoader")
        _global_loader = ConfigLoader()
    return _global_loader


def get_global_config_manager() -> ConfigManager:
    """Return a process-wide :class:`ConfigManager` instance."""

    global _global_manager
    if _global_manager is None:
        ConfigManager = __getattr__("ConfigManager")
        _global_manager = ConfigManager()
    return _global_manager


from .constants import get_min_counts_diversity_ratio

# Namespace com parametros de logging padrao
logging = SimpleNamespace(
    log_dir=importlib.import_module(".settings", __package__).logs_dir
)


def __getattr__(name: str) -> Any:
    entry = _MODULE_MAP.get(name)
    if entry is None:
        raise AttributeError(f"module 'qualia.config' has no attribute {name}")
    if isinstance(entry, tuple):
        module_path, attr = entry
    else:
        module_path, attr = entry, name
    
    # Handle relative imports
    if module_path.startswith('.'):
        module = importlib.import_module(module_path, __package__)
    else:
        module = importlib.import_module(module_path)
    return getattr(module, attr)
