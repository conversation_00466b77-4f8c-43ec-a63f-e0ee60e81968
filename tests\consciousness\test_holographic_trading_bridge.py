import asyncio
from dataclasses import dataclass
from typing import Any, Dict, Optional, List

import pytest

from src.qualia.consciousness.holographic_trading_bridge import (
    HolographicTradingBridge,
    TradingSignal,
)
from src.qualia.exchanges.base_exchange import BaseExchange


class DummyRiskManager:
    def calculate_position_size(
        self,
        symbol: str,
        current_price: float,
        stop_loss_price: Optional[float],
        confidence: float = 0.5,
        volatility: Optional[float] = None,
    ) -> Dict[str, Any]:
        return {
            "position_allowed": True,
            "quantity": 0.1,
            "reason": "ok",
        }


class RejectRiskManager(DummyRiskManager):
    def calculate_position_size(self, *a: Any, **k: Any) -> Dict[str, Any]:
        return {
            "position_allowed": False,
            "quantity": 0.0,
            "reason": "rejected",
        }


class DummyExchange(BaseExchange):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.placed_orders: List[Dict[str, Any]] = []

    async def initialize(self) -> None:
        self.connected = True

    async def get_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        return {"last": 100, "high": 110, "low": 90}

    async def place_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
    ) -> Optional[Dict[str, Any]]:
        order = {
            "id": "ord123",
            "symbol": symbol,
            "type": order_type,
            "side": side,
            "amount": amount,
            "price": 100,
        }
        self.placed_orders.append(order)
        return order

    async def get_balance(self) -> Dict[str, float]:
        return {}

    async def get_open_orders(
        self, symbol: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        return []

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        return True

    async def shutdown(self) -> None:
        self.connected = False


class FailOnCloseExchange(DummyExchange):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.fail_next: bool = False

    async def place_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
    ) -> Optional[Dict[str, Any]]:
        if self.fail_next:
            self.fail_next = False
            return None
        return await super().place_order(symbol, order_type, side, amount, price)


@pytest.mark.asyncio
async def test_bridge_executes_valid_signal() -> None:
    risk = DummyRiskManager()
    exch = DummyExchange({})
    config = {
        "min_confidence": 0.5,
        "allowed_timeframes": ["1h"],
        "trading_symbols": ["BTC/USDT"],
        "max_positions": 5,
    }
    bridge = HolographicTradingBridge(risk, exch, config)

    signal = TradingSignal(
        symbol="BTC/USDT",
        action="BUY",
        confidence=0.8,
        strength=0.9,
        timeframe="1h",
        source="test",
        rationale="",
        timestamp=0.0,
        metadata={},
    )

    result = await bridge.process_holographic_signal(signal)

    assert result.executed is True
    assert exch.placed_orders[0]["side"] == "buy"
    assert result.order_id == "ord123"


@pytest.mark.asyncio
async def test_bridge_rejects_when_risk_fails() -> None:
    risk = RejectRiskManager()
    exch = DummyExchange({})
    config = {
        "min_confidence": 0.5,
        "allowed_timeframes": ["1h"],
        "trading_symbols": ["BTC/USDT"],
        "max_positions": 5,
    }
    bridge = HolographicTradingBridge(risk, exch, config)

    signal = TradingSignal(
        symbol="BTC/USDT",
        action="BUY",
        confidence=0.8,
        strength=0.9,
        timeframe="1h",
        source="test",
        rationale="",
        timestamp=0.0,
        metadata={},
    )

    result = await bridge.process_holographic_signal(signal)

    assert result.executed is False
    assert not exch.placed_orders


@pytest.mark.asyncio
async def test_close_position_keeps_state_on_order_failure() -> None:
    risk = DummyRiskManager()
    exch = FailOnCloseExchange({})
    config = {
        "min_confidence": 0.5,
        "allowed_timeframes": ["1h"],
        "trading_symbols": ["BTC/USDT"],
        "max_positions": 5,
    }
    bridge = HolographicTradingBridge(risk, exch, config)

    signal = TradingSignal(
        symbol="BTC/USDT",
        action="BUY",
        confidence=0.8,
        strength=0.9,
        timeframe="1h",
        source="test",
        rationale="",
        timestamp=0.0,
        metadata={},
    )

    result = await bridge.process_holographic_signal(signal)

    assert result.executed is True
    assert result.order_id is not None

    exch.fail_next = True
    await bridge._close_position(result.order_id, "unit_test")

    assert result.order_id in bridge.active_positions
