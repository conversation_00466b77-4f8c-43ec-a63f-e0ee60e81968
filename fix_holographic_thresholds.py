#!/usr/bin/env python3
"""
Script para ajustar os thresholds do universo holográfico e permitir detecção de padrões.
"""

import asyncio
import logging
import time
from pathlib import Path
import sys

# Adiciona o diretório src ao path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from qualia.consciousness.holographic_universe import HolographicMarketUniverse, HolographicEvent
import numpy as np

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_with_adjusted_thresholds():
    """Testa o universo holográfico com thresholds ajustados."""
    
    print("\n=== TESTE COM THRESHOLDS AJUSTADOS ===")
    
    # 1. Inicializa o universo com configuração mais permissiva
    print("\n1. Inicializando universo com configuração ajustada...")
    
    # Configuração mais permissiva para detecção de padrões
    trading_signals_config = {
        "proximity_threshold": 50,  # Aumentado de 30 para 50
        "confidence_threshold": 0.3,  # Reduzido de 0.6 para 0.3
        "min_strength": 0.2,  # Reduzido de 0.7 para 0.2
    }
    
    universe = HolographicMarketUniverse(
        field_size=(100, 100),
        time_steps=50,
        diffusion_rate=0.1,
        feedback_strength=0.5,
        min_history_length=5,  # Reduzido de 20 para 5
        trading_signals_config=trading_signals_config
    )
    
    # Configura símbolos
    universe.symbol_positions = {
        'BTCUSDT': (25, 25),
        'ETHUSDT': (75, 75),
        'ADAUSDT': (50, 50)
    }
    
    print(f"   ✓ Universo inicializado com thresholds ajustados")
    print(f"   ✓ min_history_length: {universe.min_history_length}")
    print(f"   ✓ proximity_threshold: {trading_signals_config['proximity_threshold']}")
    print(f"   ✓ confidence_threshold: {trading_signals_config['confidence_threshold']}")
    print(f"   ✓ min_strength: {trading_signals_config['min_strength']}")
    
    # 2. Injeta eventos com amplitudes maiores
    print("\n2. Injetando eventos com amplitudes maiores...")
    
    test_events = [
        HolographicEvent(
            event_type="market_data",
            position=(25, 25),  # BTCUSDT
            amplitude=3.0,  # Aumentado de 1.5 para 3.0
            spatial_sigma=8.0,  # Aumentado
            temporal_sigma=4.0,  # Aumentado
            time=time.time(),
            source_data={"symbol": "BTCUSDT", "price": 45000.0}
        ),
        HolographicEvent(
            event_type="market_data",
            position=(75, 75),  # ETHUSDT
            amplitude=-2.5,  # Aumentado de -0.8 para -2.5
            spatial_sigma=7.0,
            temporal_sigma=3.5,
            time=time.time(),
            source_data={"symbol": "ETHUSDT", "price": 3200.0}
        ),
        HolographicEvent(
            event_type="news",
            position=(50, 50),  # ADAUSDT
            amplitude=4.0,  # Aumentado de 2.0 para 4.0
            spatial_sigma=10.0,
            temporal_sigma=5.0,
            time=time.time(),
            source_data={"title": "Crypto news", "sentiment": 0.8}
        )
    ]
    
    for i, event in enumerate(test_events):
        await universe.inject_holographic_event(event)
        print(f"   ✓ Evento {i+1} injetado: amplitude {event.amplitude}")
    
    # 3. Evolui o universo por mais passos
    print("\n3. Evoluindo universo por mais passos...")
    
    current_time = time.time()
    for step in range(15):  # Aumentado de 10 para 15
        await universe.step_evolution(current_time + step)
        field_energy = np.sum(np.abs(universe.current_field))
        if step % 3 == 0:  # Log a cada 3 passos
            print(f"   Passo {step+1}: Energia do campo = {field_energy:.4f}")
        
        await asyncio.sleep(0.05)
    
    # 4. Verifica o estado do campo
    print("\n4. Estado atual do campo:")
    field_summary = universe.get_field_summary()
    if field_summary:
        print(f"   ✓ Energia total: {field_summary.get('field_energy', 0):.4f}")
        print(f"   ✓ Entropia: {field_summary.get('field_entropy', 0):.4f}")
        print(f"   ✓ Histórico: {len(universe.field_history)} campos")
    
    # 5. Analisa padrões com debug
    print("\n5. Analisando padrões holográficos (com debug)...")
    
    # Verifica se temos histórico suficiente
    print(f"   Histórico disponível: {len(universe.field_history)} (mín: {universe.min_history_length})")
    
    # Força análise mesmo com pouco histórico (para debug)
    original_min_history = universe.min_history_length
    universe.min_history_length = min(3, len(universe.field_history))
    
    patterns = universe.analyze_holographic_patterns()
    print(f"   ✓ Padrões detectados: {len(patterns)}")
    
    # Restaura valor original
    universe.min_history_length = original_min_history
    
    for i, pattern in enumerate(patterns[:5]):
        print(f"     Padrão {i+1}: {pattern.pattern_type} em {pattern.position}")
        print(f"       - Força: {pattern.strength:.4f}")
        print(f"       - Confiança: {pattern.confidence:.4f}")
        print(f"       - Timeframe: {pattern.timeframe}")
    
    # 6. Gera sinais de trading
    print("\n6. Gerando sinais de trading...")
    signals = universe.generate_trading_signals(patterns)
    print(f"   ✓ Sinais gerados: {len(signals)}")
    
    for signal in signals:
        print(f"     {signal.symbol}: {signal.action} (força: {signal.strength:.4f})")
        print(f"       - Confiança: {signal.confidence:.4f}")
        print(f"       - Timeframe: {signal.timeframe}")
        print(f"       - Razão: {signal.rationale}")
    
    # 7. Análise detalhada se ainda não há padrões
    if len(patterns) == 0:
        print("\n7. Análise detalhada da falta de padrões...")
        
        # Verifica variação no campo
        if len(universe.field_history) >= 2:
            field_variation = np.std([np.sum(np.abs(field)) for field in universe.field_history])
            print(f"   Variação da energia do campo: {field_variation:.4f}")
            
            # Verifica alguns pontos específicos
            for symbol, pos in universe.symbol_positions.items():
                if len(universe.field_history) >= 3:
                    time_series = [field[pos[0], pos[1]] for field in universe.field_history[-3:]]
                    variation = np.std(time_series)
                    print(f"   {symbol} em {pos}: variação = {variation:.4f}")
    
    return len(patterns), len(signals)

async def main():
    """Função principal."""
    try:
        patterns_count, signals_count = await test_with_adjusted_thresholds()
        
        print("\n=== RESUMO ===")
        print(f"Padrões detectados: {patterns_count}")
        print(f"Sinais gerados: {signals_count}")
        
        if patterns_count > 0:
            print("\n✅ SUCESSO: Padrões foram detectados com os thresholds ajustados!")
        else:
            print("\n⚠️ Ainda sem padrões. Possíveis soluções:")
            print("   - Reduzir ainda mais o threshold de detecção (0.3 -> 0.1)")
            print("   - Aumentar amplitudes dos eventos")
            print("   - Verificar implementação da análise wavelet")
        
        if signals_count > 0:
            print("\n✅ SUCESSO: Sinais foram gerados!")
        
        print("\n✅ Teste concluído!")
        
    except Exception as e:
        print(f"\n❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())